<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="190" height="56.931" viewBox="0 0 190 56.931">
  <defs>
    <style>.a{fill:#5bcfff;}.b{fill:#3a4d99;}.c{fill:#426780;}.d{fill:#fff;}</style>
  </defs>
  <g transform="translate(-100 -55)">
    <path class="a" d="M109.689,55H100v9.431h9.689Z" transform="translate(0)"></path>
    <path class="b" d="M109.689,66H100v9.431h9.689Z" transform="translate(0 -1.569)"></path>
    <path class="a" d="M109.689,77H100v9.431h9.689Z" transform="translate(0 -3.137)"></path>
    <path class="c" d="M109.689,88H100v9.431h9.689Z" transform="translate(0 -4.706)"></path>
    <path class="c" d="M109.689,110.4H100v9.431h9.689Z" transform="translate(0 -7.9)"></path>
    <path class="b" d="M120.989,110.4H111.3v9.431h9.689Z" transform="translate(-1.611 -7.9)"></path>
    <path class="a" d="M143.989,110.4H134.3v9.431h9.689Z" transform="translate(-4.891 -7.9)"></path>
    <path class="b" d="M143.989,99.4H134.3v9.431h9.689Z" transform="translate(-4.891 -6.331)"></path>
    <path class="a" d="M132.689,77.2H123v9.431h9.689Z" transform="translate(-3.28 -3.166)"></path>
    <path class="b" d="M143.989,77.2H134.3v9.431h9.689Z" transform="translate(-4.891 -3.166)"></path>
    <path class="d" d="M166.029,85.317a6.086,6.086,0,0,1,.857-1.2,3.491,3.491,0,0,1,1.2-1.029,4.237,4.237,0,0,1,1.543-.686,7.621,7.621,0,0,1,4.716.257,6.332,6.332,0,0,1,2.143,1.458,6.516,6.516,0,0,1,1.372,2.229,7.545,7.545,0,0,1,.429,2.829A9.791,9.791,0,0,1,177.861,92a6.517,6.517,0,0,1-1.372,2.229,6.332,6.332,0,0,1-2.143,1.458,7.109,7.109,0,0,1-2.744.514,7.308,7.308,0,0,1-1.972-.257,6.434,6.434,0,0,1-1.543-.686,14.928,14.928,0,0,1-1.2-1.029,4.722,4.722,0,0,1-.772-1.2v2.829H164.4V77h1.715v8.317Zm10.032,1.629a6.168,6.168,0,0,0-1.029-1.8,5.737,5.737,0,0,0-1.629-1.2,6.072,6.072,0,0,0-2.229-.429,6.441,6.441,0,0,0-2.229.429,4.574,4.574,0,0,0-1.629,1.115,5.707,5.707,0,0,0-1.029,1.715A6.393,6.393,0,0,0,165.943,89a6.091,6.091,0,0,0,.343,2.229,4.812,4.812,0,0,0,1.029,1.715,4.259,4.259,0,0,0,1.629,1.115,6.01,6.01,0,0,0,4.458,0,3.861,3.861,0,0,0,1.629-1.2,7.009,7.009,0,0,0,1.029-1.715A5.92,5.92,0,0,0,176.4,89,5.764,5.764,0,0,0,176.061,86.946Z" transform="translate(-9.183 -3.137)"></path>
    <path class="d" d="M184.029,84.872a2.956,2.956,0,0,1,1.029-1.029,5.814,5.814,0,0,1,1.629-.686,8.386,8.386,0,0,1,2.229-.257,6.2,6.2,0,0,1,4.116,1.2,4.981,4.981,0,0,1,1.372,3.687V96.7h-1.715V93.789a4.791,4.791,0,0,1-1.972,2.315,5.891,5.891,0,0,1-3.087.772,4.518,4.518,0,0,1-1.972-.343,4.1,4.1,0,0,1-1.458-.857,2.634,2.634,0,0,1-.857-1.286A5.014,5.014,0,0,1,183,92.76a3.482,3.482,0,0,1,1.286-2.915,6.165,6.165,0,0,1,3.172-1.2l4.63-.429c.429-.086.686-.257.686-.6v-.343a2.607,2.607,0,0,0-.857-2.143,4.291,4.291,0,0,0-2.829-.772q-3.472,0-4.116,2.572l-1.458-.772A2.9,2.9,0,0,1,184.029,84.872Zm3.6,5.487a3.588,3.588,0,0,0-2.229.857,2.483,2.483,0,0,0,.086,3.515,3.548,3.548,0,0,0,2.315.686,5.181,5.181,0,0,0,2.058-.429A4.866,4.866,0,0,0,191.4,93.96a3.873,3.873,0,0,0,.943-1.458,5.536,5.536,0,0,0,.343-1.629V89.759Z" transform="translate(-11.836 -3.979)"></path>
    <path class="d" d="M203.458,83.657a4.677,4.677,0,0,1,2.658-.857,8.549,8.549,0,0,1,1.372.171v1.715a2.131,2.131,0,0,0-.686-.086h-.686a5.18,5.18,0,0,0-2.058.429,4.3,4.3,0,0,0-1.286,1.2,4.123,4.123,0,0,0-.686,1.886,9.789,9.789,0,0,0-.171,2.315V96.69H200.2V83.143h1.629V86.23A5.555,5.555,0,0,1,203.458,83.657Z" transform="translate(-14.288 -3.964)"></path>
    <path class="d" d="M209.714,87.187a6.517,6.517,0,0,1,1.372-2.229,8.081,8.081,0,0,1,2.144-1.543,7.552,7.552,0,0,1,2.829-.514,6.953,6.953,0,0,1,2.829.514,5.262,5.262,0,0,1,2.058,1.458,6.192,6.192,0,0,1,1.2,2.229,9.078,9.078,0,0,1,.429,2.658v.514H210.915v.171a5.616,5.616,0,0,0,.429,2.144,5.089,5.089,0,0,0,1.115,1.543,3.886,3.886,0,0,0,1.629.943,5.188,5.188,0,0,0,2.058.343,5.629,5.629,0,0,0,2.744-.6,8.4,8.4,0,0,0,1.972-1.458l.857,1.286a11.519,11.519,0,0,1-2.229,1.629,6.326,6.326,0,0,1-3.258.686,7.552,7.552,0,0,1-2.829-.514,6.517,6.517,0,0,1-2.229-1.372,6.705,6.705,0,0,1-1.458-2.229,7.691,7.691,0,0,1-.514-2.915A14.122,14.122,0,0,1,209.714,87.187Zm10.718-.086a3.873,3.873,0,0,0-.943-1.458,4.279,4.279,0,0,0-1.458-.943,5.574,5.574,0,0,0-5.487.943A5.308,5.308,0,0,0,211,88.9h9.689A6.528,6.528,0,0,0,220.432,87.1Z" transform="translate(-15.572 -3.979)"></path>
    <path class="d" d="M228.586,83.929a5.991,5.991,0,0,1,3.773-1.029,7.5,7.5,0,0,1,3.172.686,4.531,4.531,0,0,1,2.144,2.058L236.3,86.5a4.7,4.7,0,0,0-1.372-1.543,5.451,5.451,0,0,0-4.973,0A1.818,1.818,0,0,0,229.1,86.5a1.367,1.367,0,0,0,.343,1.029,2.335,2.335,0,0,0,.857.686,4.958,4.958,0,0,0,1.2.429c.429.086.943.257,1.458.343.6.086,1.115.257,1.715.343a6.439,6.439,0,0,1,1.543.6,3.661,3.661,0,0,1,1.2,1.115,3.087,3.087,0,0,1,.429,1.886,3.377,3.377,0,0,1-.429,1.715,2.529,2.529,0,0,1-1.115,1.2,3.485,3.485,0,0,1-1.629.686,8.161,8.161,0,0,1-2.143.257,12.792,12.792,0,0,1-1.8-.171A5.172,5.172,0,0,1,229.1,96.1a3.917,3.917,0,0,1-1.372-.943A4.483,4.483,0,0,1,226.7,93.7l1.372-.857a3.6,3.6,0,0,0,.772,1.2,3.263,3.263,0,0,0,1.115.772,3.593,3.593,0,0,0,1.286.343,6.93,6.93,0,0,0,1.372.086,5.11,5.11,0,0,0,2.486-.514,1.732,1.732,0,0,0,.943-1.715,2.145,2.145,0,0,0-.257-1.115,2.024,2.024,0,0,0-.772-.686,4.307,4.307,0,0,0-1.115-.429c-.429-.086-.857-.171-1.372-.257a13.705,13.705,0,0,1-1.8-.429,7.161,7.161,0,0,1-1.629-.686,3.028,3.028,0,0,1-1.2-1.115,2.814,2.814,0,0,1-.429-1.8A2.7,2.7,0,0,1,228.586,83.929Z" transform="translate(-18.067 -3.979)"></path>
    <path class="d" d="M253.661,93.689a6.087,6.087,0,0,1-.857,1.2,3.491,3.491,0,0,1-1.2,1.029,4.237,4.237,0,0,1-1.543.686,7.62,7.62,0,0,1-4.716-.257,7.3,7.3,0,0,1-2.144-1.457,6.517,6.517,0,0,1-1.372-2.229,7.545,7.545,0,0,1-.429-2.829A9.79,9.79,0,0,1,241.829,87a6.517,6.517,0,0,1,1.372-2.229,6.332,6.332,0,0,1,2.144-1.458,7.109,7.109,0,0,1,2.744-.514,7.308,7.308,0,0,1,1.972.257,6.434,6.434,0,0,1,1.543.686,14.932,14.932,0,0,1,1.2,1.029,4.723,4.723,0,0,1,.772,1.2V83.057h1.715V101.92h-1.715V93.689ZM243.543,92.06a4.812,4.812,0,0,0,1.029,1.715,5.736,5.736,0,0,0,1.629,1.2,6.072,6.072,0,0,0,2.229.429,5.617,5.617,0,0,0,2.143-.429,4.574,4.574,0,0,0,1.629-1.115,5.707,5.707,0,0,0,1.029-1.715,6.394,6.394,0,0,0,.343-2.229,6.091,6.091,0,0,0-.343-2.229,4.812,4.812,0,0,0-1.029-1.715,4.259,4.259,0,0,0-1.629-1.115,5.97,5.97,0,0,0-2.143-.429,6.072,6.072,0,0,0-2.229.429,3.861,3.861,0,0,0-1.629,1.2,6.168,6.168,0,0,0-1.029,1.8A5.92,5.92,0,0,0,243.2,90,5.466,5.466,0,0,0,243.543,92.06Z" transform="translate(-20.164 -3.964)"></path>
    <path class="d" d="M273.946,96.761h-1.629V93.675a5.036,5.036,0,0,1-4.887,3.43,6.332,6.332,0,0,1-2.4-.429,3.861,3.861,0,0,1-1.629-1.2,5.558,5.558,0,0,1-.943-1.886,10.307,10.307,0,0,1-.257-2.4V83.3h1.715v7.888a10.553,10.553,0,0,0,.171,1.715,3.346,3.346,0,0,0,.6,1.372,2.694,2.694,0,0,0,1.2.943,3.977,3.977,0,0,0,1.8.343,4.177,4.177,0,0,0,2.143-.514,3.517,3.517,0,0,0,1.372-1.372,7.278,7.278,0,0,0,.772-1.886,9.346,9.346,0,0,0,.257-2.229V83.3h1.715V96.761Z" transform="translate(-23.13 -4.036)"></path>
    <path class="d" d="M280.729,84.872a2.956,2.956,0,0,1,1.029-1.029,5.814,5.814,0,0,1,1.629-.686,8.385,8.385,0,0,1,2.229-.257,6.2,6.2,0,0,1,4.115,1.2,4.981,4.981,0,0,1,1.372,3.687V96.7h-1.715V93.789a4.791,4.791,0,0,1-1.972,2.315,5.891,5.891,0,0,1-3.087.772,4.518,4.518,0,0,1-1.972-.343,4.1,4.1,0,0,1-1.458-.857,2.634,2.634,0,0,1-.857-1.286,5.013,5.013,0,0,1-.343-1.629,3.482,3.482,0,0,1,1.286-2.915,6.165,6.165,0,0,1,3.172-1.2l4.63-.429c.429-.086.686-.257.686-.6v-.343a2.607,2.607,0,0,0-.857-2.143,4.291,4.291,0,0,0-2.829-.772q-3.472,0-4.116,2.572l-1.458-.772A2.9,2.9,0,0,1,280.729,84.872Zm3.6,5.487a3.588,3.588,0,0,0-2.229.857,2.483,2.483,0,0,0,.086,3.515,3.548,3.548,0,0,0,2.315.686,5.18,5.18,0,0,0,2.058-.429A4.866,4.866,0,0,0,288.1,93.96a3.873,3.873,0,0,0,.943-1.458,5.535,5.535,0,0,0,.343-1.629V89.759Z" transform="translate(-25.625 -3.979)"></path>
    <path class="d" d="M300.158,83.657a4.677,4.677,0,0,1,2.658-.857,8.55,8.55,0,0,1,1.372.171v1.715a2.131,2.131,0,0,0-.686-.086h-.686a5.18,5.18,0,0,0-2.058.429,4.3,4.3,0,0,0-1.286,1.2,4.123,4.123,0,0,0-.686,1.886,9.789,9.789,0,0,0-.171,2.315V96.69H296.9V83.143h1.629V86.23A5.555,5.555,0,0,1,300.158,83.657Z" transform="translate(-28.078 -3.964)"></path>
    <path class="d" d="M306.514,87.187a6.517,6.517,0,0,1,1.372-2.229,8.081,8.081,0,0,1,2.143-1.543,7.552,7.552,0,0,1,2.829-.514,6.953,6.953,0,0,1,2.829.514,5.262,5.262,0,0,1,2.058,1.458,6.193,6.193,0,0,1,1.2,2.229,9.079,9.079,0,0,1,.429,2.658v.514H307.715v.171a5.617,5.617,0,0,0,.429,2.144,5.089,5.089,0,0,0,1.115,1.543,3.886,3.886,0,0,0,1.629.943,5.188,5.188,0,0,0,2.058.343,5.629,5.629,0,0,0,2.744-.6,8.4,8.4,0,0,0,1.972-1.458l.857,1.286a11.52,11.52,0,0,1-2.229,1.629,6.326,6.326,0,0,1-3.258.686,7.552,7.552,0,0,1-2.829-.514,6.517,6.517,0,0,1-2.229-1.372,6.705,6.705,0,0,1-1.458-2.229A7.691,7.691,0,0,1,306,89.931,7.427,7.427,0,0,1,306.514,87.187Zm10.632-.086a3.873,3.873,0,0,0-.943-1.458,4.279,4.279,0,0,0-1.458-.943,5.574,5.574,0,0,0-5.487.943,5.308,5.308,0,0,0-1.543,3.258H317.4A4.377,4.377,0,0,0,317.146,87.1Z" transform="translate(-29.375 -3.979)"></path>
  </g>
</svg>
