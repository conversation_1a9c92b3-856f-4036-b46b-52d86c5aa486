version: 2.1

setup: true

orbs:
  path-filtering: circleci/path-filtering@1
  continuation: circleci/continuation@1

parameters:
  # Releases
  trigger_prod_release:
    type: boolean
    default: false

workflows:
  version: 2
  setup:
    unless: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - path-filtering/filter:
          name: check-updated-files
          base-revision: main
          config-path: .circleci/continue_config.yml
          mapping: |
            src/.* application true
            public/.* application true
            cypress/.* application true
            cypress.* application true
            package.* application true
            terraform/test/.* tf-test true
            terraform/prod/.* tf-prod true
          filters:
            tags:
              only:
                - /^prod-.*/
  setup-manual:
    when: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - continuation/continue:
          name: continue-manual-trigger
          configuration_path: .circleci/continue_config.yml
