version: 2.1

orbs:
  tf: baresquare/terraform@1.4
  releases: baresquare/releases@1.14
  slack: circleci/slack@3.4
  jira: circleci/jira@2.1
  cypress: cypress-io/cypress@3.4
executors:
  cypress-executor:
    resource_class: medium
    docker:
      - image: cypress/included:cypress-14.0.1-node-22.13.1-chrome-132.0.6834.159-1-ff-134.0.2-edge-132.0.2957.127-1

### PARAMETERS ###
parameters:
  application:
    type: boolean
    default: false
  # Terraform
  tf-test:
    type: boolean
    default: false
  tf-prod:
    type: boolean
    default: false
  # Releases
  trigger_prod_release:
    type: boolean
    default: false

### COMMANDS ###
commands:
  slack:
    parameters:
      slack_env:
        default: ''
        type: string
      job-type:
        default: ''
        type: string
    steps:
      # Message on unit-testing
      - when:
          condition:
            equal: [unit-testing, << parameters.job-type >>]
          steps:
            - run: echo "export COMMIT_MESSAGE=\"$(git log --format=oneline -n 1 $CIRCLE_SHA1 | cut -c 42- | tr -d '\"')\"" >> $BASH_ENV
            - run: echo "export PR_NUMBER=$(echo ${CIRCLE_PULL_REQUEST} | awk -F'/' '{ print $NF }')" >> $BASH_ENV
            - run: echo 'export PR_URL=https://api.github.com/repos/BareSquare/${CIRCLE_PROJECT_REPONAME}/pulls/${PR_NUMBER}' >> $BASH_ENV
            - run: echo "export TITLE=\"$(curl -s -u service-tf-systems:${GITHUB_TOKEN_SERVICE_TF_SYSTEMS} ${PR_URL} | jq -r .title | tr -d '\"')\"" >> $BASH_ENV
            - slack/status:
                success_message: ":white_check_mark: [${CIRCLE_PROJECT_REPONAME}] Unit tests passed for user: _${CIRCLE_USERNAME}_\n<${CIRCLE_PULL_REQUEST}|PR>: _${TITLE}_\nBranch: _${CIRCLE_BRANCH}_\nCommit: _${COMMIT_MESSAGE}_"
                failure_message: ":warning: [${CIRCLE_PROJECT_REPONAME}] Unit tests failed!\nUser: ${CIRCLE_USERNAME}\nBranch: _${CIRCLE_BRANCH}_\nPR: ${CIRCLE_PULL_REQUEST}"
                include_project_field: false
                include_job_number_field: false
                fail_only: true
      # Message on e2e-testing
      - when:
          condition:
            equal: [e2e-testing, << parameters.job-type >>]
          steps:
            - run: echo "export COMMIT_MESSAGE=\"$(git log --format=oneline -n 1 $CIRCLE_SHA1 | cut -c 42- | tr -d '\"')\"" >> $BASH_ENV
            - run: echo "export PR_NUMBER=$(echo ${CIRCLE_PULL_REQUEST} | awk -F'/' '{ print $NF }')" >> $BASH_ENV
            - run: echo 'export PR_URL=https://api.github.com/repos/BareSquare/${CIRCLE_PROJECT_REPONAME}/pulls/${PR_NUMBER}' >> $BASH_ENV
            - run: echo "export TITLE=\"$(curl -s -u service-tf-systems:${GITHUB_TOKEN_SERVICE_TF_SYSTEMS} ${PR_URL} | jq -r .title | tr -d '\"')\"" >> $BASH_ENV
            - slack/status:
                success_message: ":white_check_mark: [${CIRCLE_PROJECT_REPONAME}] E2E tests passed for user: _${CIRCLE_USERNAME}_\n<${CIRCLE_PULL_REQUEST}|PR>: _${TITLE}_\nBranch: _${CIRCLE_BRANCH}_\nCommit: _${COMMIT_MESSAGE}_"
                failure_message: ":warning: [${CIRCLE_PROJECT_REPONAME}] E2E tests failed!\nUser: ${CIRCLE_USERNAME}\nBranch: _${CIRCLE_BRANCH}_\nPR: ${CIRCLE_PULL_REQUEST}"
                include_project_field: false
                include_job_number_field: false
                fail_only: true

### JOBS ###
jobs:
  testing:
    parameters:
      slack_env:
        default: ''
        type: string
      jira_env:
        default: ''
        type: string
    resource_class: medium
    docker:
      - image: cimg/node:22.13.1-browsers
    steps:
      - when:
          condition:
            equal: [ false, << pipeline.parameters.application >> ]
          steps:
            - run:
                name: Skip testing
                command: circleci-agent step halt
      - checkout
      - restore_cache:
          key: deps-insights_app_fe-{{ .Environment.CACHE_VERSION }}-{{checksum "package-lock.json" }}
      - run:
          name: Install dependencies
          command: |
            npm install
      - save_cache:
          key: deps-insights_app_fe-{{ .Environment.CACHE_VERSION }}-{{checksum "package-lock.json" }}
          paths:
            - 'node_modules'
            - '~/.cache/Cypress'
      - run:
          name: Linter
          command: |
            npm run lint
      - run:
          name: Test
          command: |
            CI=true npm run test
      - cypress/run-tests:
          cypress-command: npx cypress run --headless --component
      - store_test_results:
          path: test-results
      - jira/notify:
          pipeline_id: << pipeline.id >>
          pipeline_number: << pipeline.number >>
          environment_type: << parameters.jira_env >>
          environment: << parameters.slack_env >>
          job_type: build
      - slack:
          slack_env: << parameters.slack_env >>
          job-type: unit-testing

  e2e-testing:
    parameters:
      aws_account_id:
        type: string
      aws_iam_role:
        type: string
      aws_region:
        type: string
      slack_env:
        default: ''
        type: string
    executor: cypress-executor
    steps:
      - when:
          condition:
            equal: [ false, << pipeline.parameters.application >> ]
          steps:
            - run:
                name: Skip E2E testing
                command: circleci-agent step halt
      - cypress/install:
          install-browsers: false
      - run:
          name: Install curl, jq and AWS CLI
          command: |
            apt-get update
            apt-get install -y curl jq awscli
      - run:
          name: Wait for Amplify Preview
          command: |
            PR_NUMBER=$(echo $CIRCLE_PULL_REQUEST | awk -F/ '{print $NF}')
            echo "Checking deployment for PR ${PR_NUMBER}"
            
            AMPLIFY_APP_ID=d3s9j6h26ybt3j
            MAX_ATTEMPTS=30
            ATTEMPT=1
            SLEEP_TIME=30            
            
            # Assume IAM role for TEST env
            aws sts assume-role --role-arn "arn:aws:iam::<< parameters.aws_account_id >>:role/<< parameters.aws_iam_role >>" --role-session-name AWSCLI-Session > creds.json
            export AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' creds.json)
            export AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' creds.json)
            export AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' creds.json)
            rm creds.json
            
            while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
              echo "Attempt $ATTEMPT of $MAX_ATTEMPTS"
            
              # Get latest job status  
              JOBS=$(aws amplify list-jobs --region << parameters.aws_region >> --app-id $AMPLIFY_APP_ID --branch-name "pr-${PR_NUMBER}" --max-results 50)
              LATEST_JOB=$(echo $JOBS | jq -r '.jobSummaries | sort_by(.startTime) | reverse | .[0]')
              JOB_STATUS=$(echo $LATEST_JOB | jq -r '.status')
              
              if [ "$JOB_STATUS" = "SUCCEED" ]; then
                echo "Deployment successful!"
                exit 0
              elif [ "$JOB_STATUS" = "FAILED" ] || [ "$JOB_STATUS" = "CANCELLED" ]; then
                echo "Deployment ${JOB_STATUS}"
                exit 1
              fi
              
              echo "Deployment still in progress..."
              sleep $SLEEP_TIME
              ATTEMPT=$((ATTEMPT + 1))
            done
            
            echo "Timeout waiting for deployment"
            exit 1
      - cypress/run-tests:
          cypress-command: "npx cypress run --e2e --config baseUrl=https://pr-$(echo $CIRCLE_PULL_REQUEST | awk -F/ '{print $NF}').insights.test.baresquare.com  --env username=$INSIGHTS_APP_FE_TEST_USERNAME,password=\"$INSIGHTS_APP_FE_TEST_PASSWORD\""
      - slack:
          slack_env: << parameters.slack_env >>
          job-type: e2e-testing

### WORKFLOWS ###
workflows:
  ### Application ###
  feature_branches:
    jobs:
      - testing:
          name: Unit Testing
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          slack_env: TEST
          jira_env: 'testing'
          filters:
            branches:
              ignore:
                - prod
                - main
            tags:
              ignore: /.*/
      - e2e-testing:
          name: E2E Testing
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          aws_iam_role: 'baresquare_service_insights_app_role'
          aws_account_id: '************'
          aws_region: 'us-east-1'
          slack_env: TEST
          filters:
            branches:
              ignore:
                - prod
                - main
            tags:
              ignore: /.*/
          
  test_env:
    when: << pipeline.parameters.application >>
    jobs:
      - releases/build_deploy_rebase:
          name: 'build_deploy_test'
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          service_name: 'insights-app-fe'
          slack_notification_type: 'FAILURE_ONLY'
          env: 'TEST'
          tag_prefix_prod: 'prod-r'
          git_branch_name_source: ''
          git_branch_name_target: ''
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/
  prod_env:
    jobs:
      - releases/build_deploy_rebase:
          name: 'build_deploy_prod'
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          service_name: 'insights-app-fe'
          slack_notification_type: 'FAILURE_ONLY'
          env: 'PROD'
          has_stage_env: false
          tag_prefix_prod: 'prod-r'
          git_branch_name_source: 'main'
          git_branch_name_target: 'prod'
          git_tag_name: << pipeline.git.tag >>
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          filters:
            tags:
              only: /^prod-.*/
            branches:
              ignore: /.*/
  release_to_prod:
    when: << pipeline.parameters.trigger_prod_release >>
    jobs:
      - releases/push_release_tag:
          env: 'PROD'
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          release_target_sha: ''
          tag_prefix_prod: 'prod-r'
          tag_version: 'sequence'
          has_stage_env: false
          test_env_branch_name: 'main'
          filters:
            branches:
              only: main
            tags:
              ignore: /.*/
  ### Terraform ###
  terraform-test:
    when: << pipeline.parameters.tf-test >>
    jobs:
      - tf/execute:
          name: aws-us-test
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          tf_path: 'terraform/test'
          env: 'TEST'
          github_username: 'service-tf-systems'
          github_token: ${GITHUB_TOKEN_SERVICE_TF_SYSTEMS}
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          branch_current: << pipeline.git.branch >>
          filters:
            branches:
              ignore:
                - prod
            tags:
              ignore: /.*/
  terraform-prod:
    when: << pipeline.parameters.tf-prod >>
    jobs:
      - tf/execute:
          name: aws-us-prod
          context:
            - baresquare_orb_secrets
            - baresquare_service_insights_app
          tf_path: 'terraform/prod'
          env: 'PROD'
          github_username: 'service-tf-systems'
          github_token: ${GITHUB_TOKEN_SERVICE_TF_SYSTEMS}
          circleci_pipeline_id: << pipeline.id >>
          circleci_pipeline_number: << pipeline.number >>
          branch_current: << pipeline.git.branch >>
          filters:
            branches:
              ignore:
                - prod
            tags:
              ignore: /.*/
  ### No changes ###
  no-changes:
    when:
      not:
        or:
          - << pipeline.parameters.application >>
          - << pipeline.parameters.tf-test >>
          - << pipeline.parameters.tf-prod >>
          - << pipeline.parameters.trigger_prod_release >>
    jobs:
      - tf/do_nothing:
          name: do-nothing
          filters:
            branches:
              only: main
