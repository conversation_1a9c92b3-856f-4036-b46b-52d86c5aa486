# Test Strategy for Insights App FE

## 1. Objectives

- Ensure the reliability and correctness of individual units of code
- Verify the proper functioning of React components and hooks in isolation
- Improve code quality and catch bugs early in the development process
- Facilitate easier refactoring and maintenance of the codebase
- Ensure a smooth user experience through end-to-end testing

## 2. Testing Frameworks and Tools

- Vitest: For unit testing of utility functions
- Cypress: For component testing, hook testing, and end-to-end testing

## 3. Unit Testing Strategy

- Focus on testing utility functions in the `/src/utils` directory
- Use Vitest for writing and running unit tests
- Aim for high code coverage (e.g., 80% or higher) for utility functions
- Test edge cases and error handling

## 4. Component Testing Strategy

- Use Cypress Component Testing for testing React components
- Focus on testing component behavior and user interactions
- Test both presentational and container components
- Verify proper rendering of components with different props
- Test component state changes and side effects
- Utilize Cypress commands and assertions for component interactions

## 5. Hook Testing Strategy

We will use Cypress for hook testing:

1. Create a wrapper component that uses the hook and exposes its returned values
2. Mount the wrapper component using Cypress component testing
3. Use Cypress commands to simulate user interactions and trigger hook behavior (e.g., change page or sensitivity)
4. Assert on the returned values to verify hook functionality
5. Use Cypress intercept to mock API calls and test different scenarios

Testing considerations for all hooks:

- Test initial state and loading behavior
- Verify correct data fetching and state updates
- Test error handling and edge cases
- Ensure proper cleanup (if applicable) when the component unmounts

## 6. End-to-End Testing Strategy

- Use Cypress for end-to-end testing
- Focus on critical user flows and key features
- Test user authentication and authorization
- Verify data persistence and state management across page navigation
- Test integration with backend APIs and third-party services

## 7. Test Data Management

- Create a set of mock data for incidents, user information, and API responses
- Use consistent test data across unit, component, and E2E tests
- Store mock data in a separate directory (e.g., `cypress/fixtures`)
- Use factories or fixtures to generate test data programmatically when needed

## 8. Continuous Integration

- Run all unit, component, and E2E tests on every pull request
- Set up automated test runs on the existing CI/CD platform (CircleCI)
- Block merging of pull requests if tests fail
- Implement parallel test execution to reduce CI build times

## 9. Maintenance and Refactoring

- Update tests whenever related code changes
- Regularly review and update the test suite to ensure it remains relevant and effective
- Refactor tests to improve readability and maintainability

## 10. Test Organization and File Structure

- Vitest unit tests:

  - Place test files in a `__tests__` directory within the directory of the source files they're testing
  - Use the naming convention `[filename].test.js`
  - Example: `/src/utils/__tests__/fetcher.test.js` for testing `/src/utils/fetcher.js`

- Cypress Component tests:

  - Place component test files in the same directory as the component they're testing
  - Use the naming convention `[ComponentName].cy.jsx` for React components
  - Examples:
    - `src/core/features/auth/Login.cy.jsx` for testing core authentication components
    - `src/pages/revenueInsights/RevenueInsights.cy.jsx` for testing page components
    - `src/pages/revenueInsights/components/RICard.cy.jsx` for testing page-specific components

- Cypress E2E tests:

  - Keep E2E tests in the `/cypress/e2e/` directory
  - Organize tests by feature or user flow
  - Use descriptive names like `login.cy.js`, `logout.cy.js`, etc.

- Cypress fixtures and support files:
  - Store mock data in `/cypress/fixtures/`
  - Keep custom commands and utility functions in `/cypress/support/`

## 11. Best Practices

- Write descriptive test names that explain the expected behavior
- Use beforeEach and afterEach hooks to set up and tear down test environments
- Mock external dependencies and API calls
- Use snapshot testing sparingly and only for stable components
- Keep tests independent and avoid dependencies between test cases
- Follow the Arrange-Act-Assert (AAA) pattern in test structure
- Group related tests using describe blocks
