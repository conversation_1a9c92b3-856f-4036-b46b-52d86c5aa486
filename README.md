# Insights App FE

A React based GUI created as a view of reports generated by multiple different workflows. This UI aims to create a clean UI/UX centred around the user in the most widely used Tech Stack, making it easy to develop and maintain.

## Architecture

![Insights app - Architecture](./docs/insights-app-architecture.png)

The insights-app-fe communicates with the [insights-app-be](https://github.com/BareSquare/insights-app-be), [auth0](https://manage.auth0.com/login) and GCP BigQuery, in order to retrieve it's content and authenticate/authorize the user. All backend communication goes through its dedicated BFF backend ([insights-app-bff](https://github.com/BareSquare/insights-app-bff)).

## File structure

### Entry point

- The root level HTML layout from which all the app is rendered is `index.html`. The react app is rendered inside its `#root` element.
- `/src/main.jsx` selects this element and renders the `App` component inside it.
- `/src/core/App.jsx` wraps the `Home` component inside various providers and the router.
- `/src/core/layout/Home.jsx` is the basic layout of the app.

### File structure

The project has been reorganized into three main directories at the root level of `/src`:

#### **theme/**

Contains the comprehensive theming system for the application:

- Currently contains `theme.js` with Material-UI theme configuration
- Intended to expand with styling extracted from components and organized into multiple files and subfolders
- Centralizes all design tokens, color schemes, typography, and component styling

#### **core/**

Contains reusable, project-agnostic functionality that could be used in other projects:

- **layout/**: General page structure components that apply to all views (Sidebar, Menubar, PageContainer, etc.)
- **features/**: Feature-specific modules like authentication and notifications
- **hooks/**: Custom React hooks for data fetching, caching, permissions, and roles
- **providers/**: React context providers for Auth0, SWR, and global state management
- **App.jsx** and **Routing.jsx**: Main application setup and routing configuration

#### **pages/**

Contains project-specific functionality organized by page/route:

- Each page directory (e.g., `revenueInsights`, `integrations`, `revenueForecasts`) contains:
  - Main page component
  - **components/**: Page-specific components
  - **hooks/**: Page-specific custom hooks
  - **utils/**: Page-specific utility functions (where applicable)
- This structure keeps related functionality co-located and makes the codebase easier to navigate

#### **Additional directories:**

- **utils/**: Shared utility functions used across the application
- **locales/**: Internationalization files

### Styling

Styling follows a centralized approach using the `/src/theme/` directory:

- **Primary approach**: Use Material-UI's `sx` [directive](https://mui.com/system/getting-started/the-sx-prop/) or `styled` [utility](https://mui.com/system/styled/) for component-specific styling
- **Theme configuration**: Global theme settings are defined in `/src/theme/theme.js`
- **Future expansion**: The theme directory is designed to accommodate additional styling files and subfolders as the theming system grows

**Best practices:**

- Keep component-specific styles within the component file using the `sx` prop or `styled` utility
- Use theme tokens for consistent colors, spacing, and typography
- Extract reusable styling patterns into the theme configuration

## Developer Instructions

### Prerequisites

1. After having cloned the repo, and **_before_** running `npm install`, **install** [nvm](https://github.com/nvm-sh/nvm?tab=readme-ov-file#install--update-script):
   `curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash`
2. `nvm install` and then `nvm use`. It will install and use the node version mentioned in the `.nvmrc` file.
   3`npm install`
   4(optional): Deeper shell integration for nvm. [bash](https://github.com/nvm-sh/nvm?tab=readme-ov-file#bash) | [zsh](https://github.com/nvm-sh/nvm?tab=readme-ov-file#zsh)
   5**_If_** you have already npm installed, just follow steps 1-2 (curl -> nvm install -> nvm use).

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3001](http://localhost:3001) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run cy:open:e2e`

Opens the Cypress Test Runner for E2E tests where you can select the specs to run.
_Don't forget to `npm start` first, as well as define your user's credentials in `cypress.env.json` (create it off of `cypress.env.json.example`) and define your desired **baseUrl** in `cypress.config.js` (default is http://localhost:3001)._

### `npm run cy:run:e2e`

Runs the Cypress E2E tests using headless Chrome.
_Don't forget to `npm start` first, as well as define your user's credentials in `cypress.env.json` (create it off of `cypress.env.json.example`) and define your desired **baseUrl** in `cypress.config.js` (default is http://localhost:3001)._

### `npm run cy:open:component`

Opens the Cypress Test Runner for component tests where you can select the component specs to run.

### `npm run cy:run:component`

Runs the Cypress component tests using headless Electron.

Run a single file like so:

```shell
npm run cy:run:component --  --spec src/pages/revenueInsights/RIDetails.cy.jsx
```

The `--` passes additional arguments to the script, and `--spec` specifies the test file to run.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

## Deployment

### Amplify

This app is deployed on AWS Amplify. The Repo is monitored and on every push it rebuilds and deploys the app under [insights.test.baresquare.com](https://insights.test.baresquare.com/)https://insights.test.baresquare.com/

## Test Strategy

For detailed information about our testing approach, please refer to the [Test Strategy](./docs/test-strategy.md) document.

## Authentication/Authorization

The Authentication / Authorization process is handled by Auth0 and utilizes the Organizations feature. This means that in order for the user to successfully login and see content, they have to be Authenticated against an Organization. More instructions on the invitation process can be found on [Confluence](https://bare-square.atlassian.net/l/cp/wME6yXzs)

## Local development with Docker

- Insights App FE: run `docker compose up` and access the app on http://localhost:3001
- All Insights App services: run `docker compose -f docker-compose-all-services.yml`; notes:
  - Before running the command, review the `.env` files for each service: some variables (e.g. database host) need different values when running with Docker
  - Ignore warning `volume X already exists but was created for project Y (expected Z). Use external: true ...`
  - If you get error `Error response from daemon: Conflict. The container name X is already in use by container Y. You
have to remove (or rename) that container to be able to reuse that name.`, then do:
    `shell
    $ docker rm Y  # Y is the container ID from the error message - or use the first 3 characters of the ID
    `
  - Logs: having all these containers log to the same terminal makes viewing logs difficult; you can instead view a
    container's logs like this:
    ```commandline
    $ docker ps     # check the ID of each container
    $ docker logs X # where X is the ID of the container - or its first 3 digits
    ```
