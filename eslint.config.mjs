import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";

/** @type {import('eslint').Linter.Config[]} */
export default [
  {files: ["**/*.{js,mjs,cjs,ts,jsx,tsx}"]},
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.vitest,
        cy: "readonly",
        Cypress: "readonly",
        before: "readonly",
        process: "readonly",
      }
    }
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    rules: {
      "react/react-in-jsx-scope": "off", // Disable the rule for React 17 JSX Transform
      "react/prop-types": "off",
      "@typescript-eslint/no-var-requires": "off",
      "@typescript-eslint/no-require-imports": "off",
      "react/no-unescaped-entities": "off",
      "@typescript-eslint/no-unused-vars": ["error", {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }]
    },
  },
  {
    files: ["**/*.cy.{js,jsx,ts,tsx}"], // Match Cypress test files anywhere
    rules: {
      "vitest/expect-expect": "off", // Disable expect-expect for Cypress tests
      "vitest/valid-expect": "off",  // Disable valid-expect for Cypress tests
    },
  },
  {
    ignores: ["build/", "dist/"], // Exclude the dist folder from linting
  },
  {
    settings: {
      react: {
        version: "detect", // Automatically detect the React version
      },
    },
  },
];
