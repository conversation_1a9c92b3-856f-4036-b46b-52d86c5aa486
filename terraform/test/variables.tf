### Secrets ###
# Loaded either from local .auto.tfvars files or as CI env variables
variable "GITHUB_TOKEN_SERVICE_TF_SYSTEMS_AMPLIFY_APPS" {
  type = string
}

### Data ###
data "aws_caller_identity" "this" {}

### Locals ###

locals {
  ### Platform-provided ###
  pl_env        = "test"
  pl_env_acc_id = data.aws_caller_identity.this.account_id
  pl_region     = "us-east-1"
  pl_service    = "insights-app-fe"
}