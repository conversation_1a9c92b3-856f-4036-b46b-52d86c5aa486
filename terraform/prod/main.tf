module "amplify_app" {
  source = "**************:BareSquare/terraform-modules//aws/amplify?ref=v3.18.2"

  name                          = local.pl_service
  access_token                  = var.GITHUB_TOKEN_SERVICE_TF_SYSTEMS_AMPLIFY_APPS
  repository                    = "https://github.com/BareSquare/${local.pl_service}"
  platform                      = "WEB"
  enable_auto_branch_creation   = false
  enable_basic_auth             = false
  enable_branch_auto_build      = true
  enable_branch_auto_deletion   = false
  auto_branch_creation_patterns = []
  iam_service_role_arn          = []
  build_spec                    = <<-EOT
    version: 1
    frontend:
      phases:
        preBuild:
          commands:
            - nvm install
            - nvm use
            - npm install
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
  EOT
  environment_variables = {
    PORT                                 = 80
    VITE_AUTH0_CLIENT_ID                 = "JmNk4T2pWlf7Mir744gqHL0OebAuJKV8"
    VITE_AUTH0_DOMAIN                    = "baresquare.us.auth0.com"
    VITE_AUTH0_NAMESPACE_MAIN            = "https://baresquare.com"
    VITE_CUI_GRAPE_API_AUDIENCE          = "https://use.baresquare.com/api/v1/"
    VITE_INSIGHTS_APP_API_AUTH0_AUDIENCE = "https://insights-app.prod.baresquare.com"
    VITE_INSIGHTS_APP_BFF_API_URL        = "https://insights-app-bff.baresquare.com"
    _CUSTOM_IMAGE                        = "amplify:al2023"
    _LIVE_UPDATES                        = jsonencode([{ pkg : "node", type : "nvm", version : "22.13.1" }])
  }
  custom_rules = [
    {
      source = "https://insights.baresquare.com"
      status = "301"
      target = "https://tywin.baresquare.com"
    },
    {
      source = "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|woff2|ttf|map|json)$)([^.]+$)/>"
      status = "200"
      target = "/index.html"
    }
  ]
  environments = {
    prod = {
      branch_name                 = "prod"
      enable_auto_build           = true
      backend_enabled             = false
      enable_performance_mode     = false
      enable_pull_request_preview = false
      enable_notification         = true
      framework                   = "React"
      stage                       = "PRODUCTION"
    }
  }
  domains = {
    "insights.baresquare.com" = {
      enable_auto_sub_domain = false
      wait_for_verification  = false
      sub_domain = [
        {
          branch_name     = "prod"
          prefix          = ""
          cert_type       = "AMPLIFY_MANAGED"
          custom_cert_arn = null
        }
      ]
    }
    "prod.baresquare.com" = {
      enable_auto_sub_domain = false
      wait_for_verification  = false
      sub_domain = [
        {
          branch_name     = "prod"
          prefix          = "insights"
          cert_type       = "AMPLIFY_MANAGED"
          custom_cert_arn = null
        }
      ]
    }
    "tywin.baresquare.com" = {
      enable_auto_sub_domain = false
      wait_for_verification  = false
      sub_domain = [
        {
          branch_name     = "prod"
          prefix          = ""
          cert_type       = "AMPLIFY_MANAGED"
          custom_cert_arn = null
        }
      ]
    }
  }

  pl_tags = { "service" = local.pl_service }
}
