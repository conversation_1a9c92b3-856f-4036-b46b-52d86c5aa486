terraform {
  required_version = "1.9.6"

  backend "s3" {
    bucket         = "baresquare.us-east-1.prod.terraform-remote-state"
    key            = "baresquare.us-east-1.prod.remote-state-file.insights-app-fe.json"
    region         = "us-east-1"
    dynamodb_table = "baresquare.us-east-1.prod.terraform-state-locks"
    role_arn       = "arn:aws:iam::043620330616:role/baresquare_service_insights_app_role"
    encrypt        = true
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.68.0"
    }
  }
}

provider "aws" {
  region = local.pl_region

  default_tags {
    tags = {
      env       = local.pl_env
      region    = local.pl_region
      managedby = "terraform"
    }
  }

  assume_role {
    role_arn = "arn:aws:iam::043620330616:role/baresquare_service_insights_app_role"
  }
}