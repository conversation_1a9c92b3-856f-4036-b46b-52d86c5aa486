{"name": "insights-app-fe", "version": "0.1.0", "private": true, "engines": {"node": "22.13.1"}, "dependencies": {"@auth0/auth0-react": "2.3.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/react-fontawesome": "0.2.2", "@mui/icons-material": "6.4.1", "@mui/material": "6.4.1", "@rjsf/core": "^5.24.2", "@rjsf/mui": "^5.24.2", "@rjsf/utils": "^5.24.2", "@rjsf/validator-ajv8": "^5.24.2", "d3-format": "^3.1.0", "dayjs": "1.11.13", "dompurify": "3.2.4", "html-react-parser": "5.2.2", "jwt-decode": "^4.0.0", "microdiff": "1.5.0", "plotly.js-dist-min": "2.35.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "10.1.0", "react-plotly.js": "2.6.0", "react-router-dom": "7.1.1", "swr": "2.3.0", "video.js": "^8.21.0", "web-vitals": "4.2.4"}, "scripts": {"format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "preview": "vite build --mode development && vite preview --mode development", "start": "vite", "build": "vite build", "test": "vitest", "prepare": "husky", "cy:open:e2e": "cypress open --e2e --browser chrome", "cy:run:e2e": "cypress run --e2e --browser chrome", "cy:open:component": "cypress open --component", "cy:run:component": "cypress run --component", "lint": "eslint . --cache --fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@cypress/vite-dev-server": "^6.0.2", "@eslint/js": "^9.19.0", "@vitejs/plugin-react": "^4.3.4", "cypress": "^14.0.1", "cypress-network-idle": "^1.15.0", "dotenv": "^16.4.7", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "prettier": "3.4.2", "rollup-plugin-visualizer": "^5.14.0", "typescript-eslint": "^8.22.0", "vite": "^6.0.11", "vite-plugin-compression": "^0.5.1", "vitest": "^3.0.4"}, "lint-staged": {"*.{js,jsx,css,md}": "prettier --write"}}