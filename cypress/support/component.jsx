import './commands';
import { cloneElement } from 'react';
import { mount } from 'cypress/react';
import StateProvider, { State } from '../../src/core/providers/State';
import { ThemeProvider } from '@mui/material/styles';
import { BrowserRouter as Router } from 'react-router-dom';
import theme from '../../src/theme/theme';
import Auth0ProviderWithHistory from '../../src/core/providers/Auth0ProviderWithHistory';
import SWRProvider from '../../src/core/providers/SWRProvider';
import { SWRConfig } from 'swr';
import { Auth0Context } from '@auth0/auth0-react';
import Fetcher from '../../src/utils/fetcher';

Cypress.Commands.add('mount', (component, options = {}) => {
  const wrapped = (
    <StateProvider>
      <ThemeProvider theme={theme}>
        <Router>
          <Auth0ProviderWithHistory
            audience={Cypress.env('VITE_INSIGHTS_APP_API_AUTH0_AUDIENCE')}
            domain={Cypress.env('VITE_AUTH0_DOMAIN')}
            clientId={Cypress.env('VITE_AUTH0_CLIENT_ID')}
          >
            <SWRProvider>{component}</SWRProvider>
          </Auth0ProviderWithHistory>
        </Router>
      </ThemeProvider>
    </StateProvider>
  );

  return mount(wrapped, options);
});

Cypress.Commands.add(
  'mountWithSWR',
  (
    ui,
    { auth0OrgID = 'org_wGOui4siTYehagCg', userRoles = ['Insights App User'], ...options } = {},
  ) => {
    const mockAuth0 = {
      getAccessTokenSilently: cy.stub().resolves('mock-token'),
      isAuthenticated: true,
      user: { [`${Cypress.env('VITE_AUTH0_NAMESPACE_MAIN')}/roles`]: userRoles },
    };

    const mockState = {
      state: { httpErrorNotifVisible: false, auth0OrgID, displayName: 'mockOrgName' },
      updateState: cy.spy().as('updateState'),
    };

    return cy.mount(
      <Auth0Context.Provider value={mockAuth0}>
        <SWRConfig value={{ provider: () => new Map(), fetcher: Fetcher('fake-token') }}>
          <State.Provider value={mockState}>{cloneElement(ui)}</State.Provider>
        </SWRConfig>
      </Auth0Context.Provider>,
      options,
    );
  },
);

Cypress.Commands.add('mountAuthorized', (component, userRoles = []) => {
  const getAccessTokenSilently = cy.stub().resolves('mock-token');
  const isAuthenticated = true;
  const user = { [`${Cypress.env('VITE_AUTH0_NAMESPACE_MAIN')}/roles`]: userRoles };

  return cy.mount(
    <Auth0Context.Provider value={{ getAccessTokenSilently, isAuthenticated, user }}>
      {cloneElement(component)}
    </Auth0Context.Provider>,
  );
});

Cypress.Commands.add(
  'checkPaginationAndIncidents',
  (page, totalCount, firstIncident, lastIncident) => {
    const start = (page - 1) * 20 + 1;
    const end = Math.min(page * 20, totalCount);
    cy.get('[data-testid="pagination-control"] .MuiTablePagination-displayedRows')
      .eq(0)
      .should('have.text', `${start} – ${end} of ${totalCount}`);
    cy.get('[data-testid^="incident-dimension-"]').first().should('contain', firstIncident);
    cy.get('[data-testid^="incident-dimension-"]').last().should('contain', lastIncident);
  },
);
