/**
 * @memberof cy
 * @method loginViaUI
 * @param {string} email
 * @param {string} password
 * @returns Chainable
 */
Cypress.Commands.add('loginViaUI', (email, password) => {
  const args = { email, password };

  cy.intercept('GET', '/api/v1/organizations/*/insights*').as('getInsights');
  cy.intercept('GET', '**/flow_instances').as('getFlowInstances');

  cy.visit('/');

  cy.origin(Cypress.env('VITE_AUTH0_DOMAIN'), { args }, ({ email, password }) => {
    cy.get('#username').type(email, { delay: 0 });
    cy.contains('button', 'Continue').click();
    cy.get('#password').type(password, { log: false, delay: 0 });
    cy.get('[data-action-button-primary="true"]').click();

    // Check if we're redirected to organization picker
    cy.url().then(url => {
      if (url.includes('/organization-picker')) {
        // Find and click the button with the specified org
        cy.get('button img[alt="insightsApp-e2eOrg"]').should('exist').closest('button').click();
      }
    });
  });

  cy.wait('@getFlowInstances', { timeout: 20_000 })
    .its('response')
    .then(response => {
      expect(response.statusCode).to.eq(200);
      expect(response.body.flowInstances).to.satisfy(flowInstances =>
        flowInstances.some(instance => instance.flow.slug === 'revenue-insights'),
      );
    });
  cy.wait('@getInsights', { timeout: 20_000 }).its('response.statusCode').should('eq', 200);
  cy.location('pathname', { timeout: 0 }).should('eq', '/revenue-insights');

  cy.getAllCookies().then(cookies => {
    cy.wrap(cookies.some(obj => obj.name === 'auth0')).should('be.true');
  });
});
