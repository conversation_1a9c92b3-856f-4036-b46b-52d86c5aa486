import './commands';
import 'cypress-network-idle';

before(() => {
  // Caching login session
  cy.session(
    'Authentication',
    () => {
      const username = Cypress.env('username');
      const password = Cypress.env('password');

      cy.loginViaUI(username, password);
    },
    {
      cacheAcrossSpecs: true,
      validate() {
        cy.visit('/revenue-insights').waitForNetworkIdle(1000);
        cy.location('pathname', { timeout: 0 }).should('eq', '/revenue-insights');
      },
    },
  );
});
