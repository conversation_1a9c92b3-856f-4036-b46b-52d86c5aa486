{"__typename": "FlowInstance", "id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "integrations": [{"__typename": "FlowInstanceIntegration", "connector_metadata": {"account_id": {"label": "Account id", "value": "********"}, "property_id": {"label": "Property id", "value": "*********"}, "website_url": {"label": "Website URL", "value": "https://www.bebe.com/"}, "timezone": {"label": "Timezone", "value": "United States - (GMT-07:00) Los Angeles Time"}}, "flow_instance_id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "id": "ee7b31e7-2554-41b0-bd12-821cb2036c5f", "status": "active", "integration_id": "c9a538c4-a4a3-49e0-86e2-e17ca295f32f", "integration": {"__typename": "Integration", "description": "Turn website data into user behavior insights to improve website performance and enhance marketing strategies."}, "connector": {"__typename": "Connector", "id": "0446d22d-3d4a-4bc5-9a25-7f2dd09061e9", "name": "Google Analytics 4", "icon": "/connectors/ga4.png", "description": null}}, {"__typename": "FlowInstanceIntegration", "connector_metadata": {"token_friendly_name": {"label": "Token name", "value": "Clarity Data Export API token"}, "access_token": {"label": "Token value", "value": "*****1fm0qA"}, "project_name": {"label": "Project", "value": "N/A"}}, "flow_instance_id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "id": "305ead1b-3304-49ab-b917-300c2c6d498b", "status": "active", "integration_id": "b6aced6f-2d05-4b5f-8b76-7877b272a802", "integration": {"__typename": "Integration", "description": "Improve user satisfaction by identifying and addressing website errors, enhancing overall user experience and revenue funnel performance."}, "connector": {"__typename": "Connector", "id": "3bd92493-97aa-4fc3-b742-3661920ec0be", "name": "Microsoft Clarity", "icon": "/connectors/clarity.png", "description": null}}, {"__typename": "FlowInstanceIntegration", "connector_metadata": {"website-friendly-domain": {"label": "Website domain", "value": "bebe.com"}, "shopify-url": {"label": "Shopify URL", "value": "https://bebe-dev.myshopify.com/"}, "app-id": {"label": "App id", "value": "N/A", "info": "If supplied, we can deep link into the connected App"}, "app-name": {"label": "App name", "value": "", "info": "The app for which the customer-generated a token"}, "access-token": {"label": "Access token", "value": "", "info": "Generate a unique token for <PERSON><PERSON><PERSON><PERSON>"}}, "flow_instance_id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "id": "c48c807d-e060-454d-a287-ee9cecd35cb2", "status": "active", "integration_id": "543004a9-6c30-4212-8e3a-7ccc1d8f09ce", "integration": {"__typename": "Integration", "description": "Monitor product availability to explain revenue shifts due to stockouts and technical issues.    "}, "connector": {"__typename": "Connector", "id": "6fc0336d-cbf9-44bf-9283-a9bd9d261f7d", "name": "Shopify", "icon": "/connectors/shopify.svg", "description": null}}], "organization_id": "org_h6IxfMHUtMlok2om", "status": "active", "flow": {"__typename": "Flow", "description": null, "id": "0441dd70-2a4b-4a22-b0ed-ccd6d6f04ac5", "name": "e-commerce", "status": "active", "integrations": [{"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "38b1e0f6-6c01-4293-a4f2-6f3bfeb103c9", "name": "Adobe Analytics", "icon": "/connectors/aa.png", "description": null}, {"__typename": "Connector", "id": "0446d22d-3d4a-4bc5-9a25-7f2dd09061e9", "name": "Google Analytics 4", "icon": "/connectors/ga4.png", "description": null}], "description": "Turn website data into user behavior insights to improve website performance and enhance marketing strategies.", "icon": "Timeline", "id": "c9a538c4-a4a3-49e0-86e2-e17ca295f32f", "label": "Primary", "name": "Web analytics", "required": true, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "6fc0336d-cbf9-44bf-9283-a9bd9d261f7d", "name": "Shopify", "icon": "/connectors/shopify.svg", "description": null}], "description": "Monitor product availability to explain revenue shifts due to stockouts and technical issues.    ", "icon": "Inventory", "id": "543004a9-6c30-4212-8e3a-7ccc1d8f09ce", "label": "Secondary", "name": "Product inventory", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "60e38082-c2cd-44cd-badb-2fd8045aa85e", "name": "Google Sheets", "icon": "/connectors/gsheets.png", "description": null}], "description": "Maximize profitability by effectively managing revenue targets across various product categories.", "icon": "RequestQuote", "id": "bfa48d35-3360-44c5-95dd-c27a4425090a", "label": "Secondary", "name": "Revenue plans", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "3bd92493-97aa-4fc3-b742-3661920ec0be", "name": "Microsoft Clarity", "icon": "/connectors/clarity.png", "description": null}], "description": "Improve user satisfaction by identifying and addressing website errors, enhancing overall user experience and revenue funnel performance.", "icon": "ImportantDevices", "id": "b6aced6f-2d05-4b5f-8b76-7877b272a802", "label": "Secondary", "name": "UX analytics", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [], "description": "Understand user behavior with AI-driven session replays, revealing key interaction patterns and usability improvements.", "icon": "OndemandVideo", "id": "7a3f0427-e7ab-413a-88d5-e0f8ac6e5a34", "label": "Secondary", "name": "Session replay", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "5c056f37-8961-4d61-af7d-86662b2e3bf7", "name": "JIRA", "icon": "/connectors/jira.png", "description": null}], "description": "Enhance teamwork and productivity with effortless collaboration features, allowing easy sharing of insights on your preferred platforms.", "icon": "Groups", "id": "bb4ce599-9134-4311-985d-014d6ffa6ffa", "label": "Outbound", "name": "Collaboration", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "84148f82-54aa-461a-9575-2132e570f29b", "name": "<PERSON><PERSON>ck", "icon": "/connectors/slack.png", "description": null}], "description": "Stay updated with personalized Baresquare insights delivered directly to your favorite platforms, ensuring you never miss important information.", "icon": "Notifications", "id": "5be98870-b645-496b-9959-ba5974c449bb", "label": "Outbound", "name": "Notification", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "60e38082-c2cd-44cd-badb-2fd8045aa85e", "name": "Google Sheets", "icon": "/connectors/gsheets.png", "description": null}], "description": "Streamline your marketing efforts by integrating promotional and special event calendars, ensuring timely and impactful campaigns.", "icon": "Event", "id": "d2d60148-94c9-4c6e-80c8-e7cda25f7893", "label": "Secondary", "name": "Promo calendars", "required": false, "status": "active"}]}, "grouped_flow_integrations": {"Primary": [{"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "38b1e0f6-6c01-4293-a4f2-6f3bfeb103c9", "name": "Adobe Analytics", "icon": "/connectors/aa.png", "description": null}, {"__typename": "Connector", "id": "0446d22d-3d4a-4bc5-9a25-7f2dd09061e9", "name": "Google Analytics 4", "icon": "/connectors/ga4.png", "description": null}], "description": "Turn website data into user behavior insights to improve website performance and enhance marketing strategies.", "icon": "Timeline", "id": "c9a538c4-a4a3-49e0-86e2-e17ca295f32f", "label": "Primary", "name": "Web analytics", "required": true, "status": "active"}], "Secondary": [{"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "6fc0336d-cbf9-44bf-9283-a9bd9d261f7d", "name": "Shopify", "icon": "/connectors/shopify.svg", "description": null}], "description": "Monitor product availability to explain revenue shifts due to stockouts and technical issues.    ", "icon": "Inventory", "id": "543004a9-6c30-4212-8e3a-7ccc1d8f09ce", "label": "Secondary", "name": "Product inventory", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "60e38082-c2cd-44cd-badb-2fd8045aa85e", "name": "Google Sheets", "icon": "/connectors/gsheets.png", "description": null}], "description": "Maximize profitability by effectively managing revenue targets across various product categories.", "icon": "RequestQuote", "id": "bfa48d35-3360-44c5-95dd-c27a4425090a", "label": "Secondary", "name": "Revenue plans", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "3bd92493-97aa-4fc3-b742-3661920ec0be", "name": "Microsoft Clarity", "icon": "/connectors/clarity.png", "description": null}], "description": "Improve user satisfaction by identifying and addressing website errors, enhancing overall user experience and revenue funnel performance.", "icon": "ImportantDevices", "id": "b6aced6f-2d05-4b5f-8b76-7877b272a802", "label": "Secondary", "name": "UX analytics", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [], "description": "Understand user behavior with AI-driven session replays, revealing key interaction patterns and usability improvements.", "icon": "OndemandVideo", "id": "7a3f0427-e7ab-413a-88d5-e0f8ac6e5a34", "label": "Secondary", "name": "Session replay", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "60e38082-c2cd-44cd-badb-2fd8045aa85e", "name": "Google Sheets", "icon": "/connectors/gsheets.png", "description": null}], "description": "Streamline your marketing efforts by integrating promotional and special event calendars, ensuring timely and impactful campaigns.", "icon": "Event", "id": "d2d60148-94c9-4c6e-80c8-e7cda25f7893", "label": "Secondary", "name": "Promo calendars", "required": false, "status": "active"}], "Outbound": [{"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "5c056f37-8961-4d61-af7d-86662b2e3bf7", "name": "JIRA", "icon": "/connectors/jira.png", "description": null}], "description": "Enhance teamwork and productivity with effortless collaboration features, allowing easy sharing of insights on your preferred platforms.", "icon": "Groups", "id": "bb4ce599-9134-4311-985d-014d6ffa6ffa", "label": "Outbound", "name": "Collaboration", "required": false, "status": "active"}, {"__typename": "Integration", "connectors": [{"__typename": "Connector", "id": "84148f82-54aa-461a-9575-2132e570f29b", "name": "<PERSON><PERSON>ck", "icon": "/connectors/slack.png", "description": null}], "description": "Stay updated with personalized Baresquare insights delivered directly to your favorite platforms, ensuring you never miss important information.", "icon": "Notifications", "id": "5be98870-b645-496b-9959-ba5974c449bb", "label": "Outbound", "name": "Notification", "required": false, "status": "active"}]}, "mapped_flow_instance_integrations": {"c9a538c4-a4a3-49e0-86e2-e17ca295f32f": {"__typename": "FlowInstanceIntegration", "connector_metadata": {"account_id": {"label": "Account id", "value": "********"}, "property_id": {"label": "Property id", "value": "*********"}, "website_url": {"label": "Website URL", "value": "https://www.bebe.com/"}, "timezone": {"label": "Timezone", "value": "United States - (GMT-07:00) Los Angeles Time"}}, "flow_instance_id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "id": "ee7b31e7-2554-41b0-bd12-821cb2036c5f", "status": "active", "integration_id": "c9a538c4-a4a3-49e0-86e2-e17ca295f32f", "integration": {"__typename": "Integration", "description": "Turn website data into user behavior insights to improve website performance and enhance marketing strategies."}, "connector": {"__typename": "Connector", "id": "0446d22d-3d4a-4bc5-9a25-7f2dd09061e9", "name": "Google Analytics 4", "icon": "/connectors/ga4.png", "description": null}}, "b6aced6f-2d05-4b5f-8b76-7877b272a802": {"__typename": "FlowInstanceIntegration", "connector_metadata": {"token_friendly_name": {"label": "Token name", "value": "Clarity Data Export API token"}, "access_token": {"label": "Token value", "value": "*****1fm0qA"}, "project_name": {"label": "Project", "value": "N/A"}}, "flow_instance_id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "id": "305ead1b-3304-49ab-b917-300c2c6d498b", "status": "active", "integration_id": "b6aced6f-2d05-4b5f-8b76-7877b272a802", "integration": {"__typename": "Integration", "description": "Improve user satisfaction by identifying and addressing website errors, enhancing overall user experience and revenue funnel performance."}, "connector": {"__typename": "Connector", "id": "3bd92493-97aa-4fc3-b742-3661920ec0be", "name": "Microsoft Clarity", "icon": "/connectors/clarity.png", "description": null}}, "543004a9-6c30-4212-8e3a-7ccc1d8f09ce": {"__typename": "FlowInstanceIntegration", "connector_metadata": {"website-friendly-domain": {"label": "Website domain", "value": "bebe.com"}, "shopify-url": {"label": "Shopify URL", "value": "https://bebe-dev.myshopify.com/"}, "app-id": {"label": "App id", "value": "N/A", "info": "If supplied, we can deep link into the connected App"}, "app-name": {"label": "App name", "value": "", "info": "The app for which the customer-generated a token"}, "access-token": {"label": "Access token", "value": "", "info": "Generate a unique token for <PERSON><PERSON><PERSON><PERSON>"}}, "flow_instance_id": "bcbc73c1-a288-4548-9fe7-0f0d1332c918", "id": "c48c807d-e060-454d-a287-ee9cecd35cb2", "status": "active", "integration_id": "543004a9-6c30-4212-8e3a-7ccc1d8f09ce", "integration": {"__typename": "Integration", "description": "Monitor product availability to explain revenue shifts due to stockouts and technical issues.    "}, "connector": {"__typename": "Connector", "id": "6fc0336d-cbf9-44bf-9283-a9bd9d261f7d", "name": "Shopify", "icon": "/connectors/shopify.svg", "description": null}}}}