const checkAuthDomain = () => {
  cy.origin(Cypress.env('VITE_AUTH0_DOMAIN'), () => {
    cy.location('host').should('eq', Cypress.env('VITE_AUTH0_DOMAIN'));
  });
};

describe('Logout functionality', () => {
  before(() => {
    cy.visit('/');
    cy.waitForNetworkIdle(5000);
    cy.get('[data-test-id="logout"]').should('be.visible').click();
    cy.waitForNetworkIdle(5000);
  });

  it('redirects to auth page', () => {
    checkAuthDomain();

    cy.visit('/revenue-insights').waitForNetworkIdle(5000);
    cy.location('pathname', { timeout: 0 }).should('not.eq', '/revenue-insights');

    checkAuthDomain();
  });
});
