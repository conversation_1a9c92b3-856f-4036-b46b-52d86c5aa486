import { useState } from 'react';
import { Typography } from '@mui/material';
import PageTitle from '@core/layout/PageTitle';
import PageContainer from '@core/layout/PageContainer';
import useForecasts from './hooks/useForecasts';
import useForecast from './hooks/useForecast';
import useLatestForecast from './hooks/useLatestForecast';
import {
  PlansTable,
  PeriodSelector,
  RevenueGroupSelector,
  ForecastChart,
  FiltersContainer,
  ContentContainer,
  SectionContainer,
  SectionTitle,
  UtilityButtons,
  TableTitleContainer,
} from './components';
import { renderHtml } from '../../utils/textUtils';
import { dayjs } from '../../utils/dateUtils';
import { convertForecastToRows } from './utils/data';
import Loader from '@core/features/notifications/Loader';

const defaultRevenueGroups = ['total'];

export default function RevenueForecasts() {
  // load default period (latest) & forecasts list

  const { forecast: latestForecast, error: latestError } = useLatestForecast();
  if (latestError?.status === 404) console.log('whooops');

  const { forecasts } = useForecasts();
  const defaultPeriod = forecasts && latestForecast ? latestForecast.id : undefined;

  // load report on select

  const [selectedForecastId, setSelectedForecastId] = useState(defaultPeriod);

  const { forecast: selectedForecast, isLoading: isLoadingForecast } =
    useForecast(selectedForecastId);

  const forecast = selectedForecast ?? latestForecast;

  // revenue groups
  const revenueGroups = forecast?.revenueGroups.map(x => x.name);
  const [selectedRevenueGroups, setSelectedRevenueGroups] = useState(defaultRevenueGroups);

  const selectPeriod = event => {
    setSelectedForecastId(event.target.value);
    setSelectedRevenueGroups(defaultRevenueGroups);
  };

  const showFilters = !!defaultPeriod;
  const showSummary = !!defaultPeriod && !isLoadingForecast;
  const showChart = defaultPeriod && selectedRevenueGroups.length > 0 && !isLoadingForecast;
  const showTable = defaultPeriod && selectedRevenueGroups.length > 0 && !isLoadingForecast;

  const tableData = convertForecastToRows(forecast, selectedRevenueGroups);

  const week = dayjs(forecast?.periodStart).week();

  return (
    <PageContainer>
      <PageTitle title="Revenue-stream Forecasting Agent" />
      <ContentContainer>
        {!showFilters && <Loader />}
        {showFilters && (
          <FiltersContainer>
            <PeriodSelector
              defaultPeriod={defaultPeriod}
              selectPeriod={selectPeriod}
              forecasts={forecasts}
            />

            <RevenueGroupSelector
              revenueGroups={revenueGroups}
              selectedRevenueGroups={selectedRevenueGroups}
              setSelectedRevenueGroups={setSelectedRevenueGroups}
            />
          </FiltersContainer>
        )}

        {isLoadingForecast && <Loader />}
        {showSummary && (
          <SectionContainer>
            <SectionTitle title="Summary" />
            <Typography>{renderHtml(forecast.summary)}</Typography>
          </SectionContainer>
        )}

        {showChart && (
          <SectionContainer>
            <SectionTitle title="Revenue (Actual & Forecast)" />
            <ForecastChart forecast={forecast} selectedRevenueGroups={selectedRevenueGroups} />
          </SectionContainer>
        )}

        {showTable && (
          <SectionContainer>
            <TableTitleContainer>
              <SectionTitle title={`Revenue Forecasts for Week ${week}`} />
              <UtilityButtons
                forecast={forecast}
                data={tableData}
                selectedRevenueGroups={selectedRevenueGroups}
              />
            </TableTitleContainer>
            <PlansTable data={tableData} selectedRevenueGroups={selectedRevenueGroups} />
          </SectionContainer>
        )}
      </ContentContainer>
    </PageContainer>
  );
}
