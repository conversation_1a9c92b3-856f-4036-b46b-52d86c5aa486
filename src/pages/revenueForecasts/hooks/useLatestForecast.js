import useSWR from 'swr';
import { useContext } from 'react';
import { State } from '@core/providers/State';

const useLatestForecast = () => {
  const {
    state: { auth0OrgID },
  } = useContext(State);

  const url =
    import.meta.env.VITE_INSIGHTS_APP_BFF_API_URL +
    `/api/v1/organizations/${auth0OrgID}/forecasts/latest`;

  const { data, error, isLoading } = useSWR(url, { shouldRetryOnError: false });

  return {
    forecast: data,
    error,
    isLoading,
  };
};

export default useLatestForecast;
