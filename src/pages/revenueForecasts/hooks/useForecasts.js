import useSWR from 'swr';
import { useContext } from 'react';
import { State } from '@core/providers/State';

const useForecasts = () => {
  const {
    state: { auth0OrgID },
  } = useContext(State);

  const url =
    import.meta.env.VITE_INSIGHTS_APP_BFF_API_URL + `/api/v1/organizations/${auth0OrgID}/forecasts`;

  const { data, error, isLoading } = useSWR(url, { shouldRetryOnError: false });

  return {
    forecasts: data?.forecasts,
    error,
    isLoading,
  };
};

export default useForecasts;
