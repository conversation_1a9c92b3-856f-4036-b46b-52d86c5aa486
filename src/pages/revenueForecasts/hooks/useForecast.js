import useSWR from 'swr';
import { useContext } from 'react';
import { State } from '@core/providers/State';

const useForecast = forecastId => {
  const {
    state: { auth0OrgID },
  } = useContext(State);

  const url =
    import.meta.env.VITE_INSIGHTS_APP_BFF_API_URL +
    `/api/v1/organizations/${auth0OrgID}/forecasts/${forecastId}`;

  const { data, error, isLoading } = useSWR(forecastId ? url : null, { shouldRetryOnError: false });

  return {
    forecast: data,
    error,
    isLoading,
  };
};

export default useForecast;
