import Plot from 'react-plotly.js';
import dayjs from 'dayjs';

const ForecastChart = ({ forecast, selectedRevenueGroups }) => {
  const selectedData = forecast.revenueGroups.filter(x => selectedRevenueGroups.includes(x.name));

  const traces = selectedData.flatMap((data, groupIndex) => {
    const historicalDates = data.historical.map(d => d.date);
    const historicalValues = data.historical.map(d => d.revenue);

    const allSpecialDates = [
      ...data.historical.filter(d => !!d.special_date),
      ...data.forecast.filter(d => !!d.special_date),
    ];
    const specialDates = allSpecialDates.map(d => d.date);
    const specialValues = allSpecialDates.map(d => d.revenue);
    const specialDatesTypes = allSpecialDates.map(d => d.special_date);

    const forecastDates = data.forecast.map(d => d.date);
    const forecastValues = data.forecast.map(d => d.revenue);
    const forecastLower = data.forecast.map(d => d.lower);
    const forecastUpper = data.forecast.map(d => d.upper);

    const color = `hsl(${(groupIndex * 360) / selectedData.length}, 70%, 50%)`;

    return [
      {
        x: historicalDates,
        y: historicalValues,
        type: 'scatter',
        mode: 'lines',
        name: `${data.name}`,
        line: {
          color: color,
          shape: 'spline',
        },
        hovertemplate: `${forecast?.keyMetric?.symbol}%{y:,.0f}<extra></extra>`,
      },
      {
        x: [historicalDates[historicalDates.length - 1], forecastDates[0]],
        y: [historicalValues[historicalValues.length - 1], forecastValues[0]],
        type: 'scatter',
        mode: 'lines',
        line: {
          color: color,
          shape: 'spline',
        },
        showlegend: false,
        hovertemplate: '$%{y}<extra></extra>',
      },
      {
        x: forecastDates,
        y: forecastValues,
        type: 'scatter',
        mode: 'lines+markers',
        name: `${data.name} - Forecast`,
        line: {
          dash: 'dot',
          color: color,
          shape: 'spline',
        },
        showlegend: false,
        hovertemplate: '$%{y:,.0f}<extra></extra>',
      },
      {
        x: [...forecastDates, ...forecastDates.slice().reverse()],
        y: [...forecastUpper, ...forecastLower.slice().reverse()],
        fill: 'toself',
        fillcolor: `hsla(${(groupIndex * 360) / selectedData.length}, 70%, 50%, 0.2)`,
        line: { color: 'transparent' },
        type: 'scatter',
        showlegend: false,
        name: `${data.name} - Confidence Interval`,
        hovertemplate: '$%{y:,.0f}<extra></extra>',
      },
      {
        x: specialDates,
        y: specialValues,
        mode: 'markers',
        type: 'scatter',
        name: 'Special Dates',
        marker: {
          size: 10,
          color: color,
        },
        showlegend: groupIndex === selectedData.length - 1,
        customdata: specialDatesTypes,
        hovertemplate: '$%{y:,.0f}K<extra>%{customdata}</extra>',
      },
    ];
  });

  const today = dayjs(selectedData[0].forecast[0].date).format('YYYY-MM-DD');

  return (
    <Plot
      data={traces}
      layout={{
        dragmode: false,
        title: '',
        margin: { l: 40, r: 20, t: 20, b: 40 },
        xaxis: {
          title: '',
          showgrid: false,
          zeroline: false,
        },
        yaxis: {
          title: '',
          showline: false,
          tickprefix: forecast?.keyMetric?.symbol,
          tickformat: '.0s',
        },
        shapes: [
          {
            type: 'line',
            x0: today,
            x1: today,
            y0: 0,
            yref: 'paper',
            y1: 1,
            line: {
              color: 'gray',
              width: 1,
              dash: 'dot',
            },
          },
        ],
        legend: {
          orientation: 'h',
          y: -0.3,
        },
      }}
      config={{
        responsive: true,
        fillFrame: false,
        scrollZoom: false,
        displaylogo: false,
        displayModeBar: false,
      }}
      style={{ width: '100%', height: '500px' }}
    />
  );
};

export default ForecastChart;
