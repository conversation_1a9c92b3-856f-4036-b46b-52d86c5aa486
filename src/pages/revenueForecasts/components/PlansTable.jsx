import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';

export default function PlansTable({ data, selectedRevenueGroups }) {
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Date</TableCell>
            {selectedRevenueGroups.map(group => (
              <TableCell align="left" key={group}>
                {group}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map(row => (
            <TableRow key={row.date} hover>
              <TableCell>{row.date}</TableCell>
              {...selectedRevenueGroups.map(group => (
                <TableCell align="left" key={group}>
                  {row[group]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
