import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import { dayjs } from '../../../utils/dateUtils';

export default function PeriodSelector({ defaultPeriod, selectPeriod, forecasts }) {
  return (
    <FormControl sx={{ flex: 1 }}>
      <InputLabel id="forecast-select-label">Forecast Period</InputLabel>
      <Select
        sx={{
          backgroundColor: theme =>
            theme.components?.MuiTextField?.styleOverrides?.root?.backgroundColor,
        }}
        labelId="forecast-select-label"
        id="forecast-select"
        label="Forecast Period"
        defaultValue={defaultPeriod}
        onChange={selectPeriod}
      >
        {forecasts?.map(forecast => (
          <MenuItem key={forecast.id} value={forecast.id}>
            {`Week ${dayjs(forecast.periodStart).week()} ` +
              `(${new Date(forecast.periodStart).toLocaleDateString()} - ` +
              `${new Date(dayjs(forecast.periodStart).add(6, 'day')).toLocaleDateString()})`}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
