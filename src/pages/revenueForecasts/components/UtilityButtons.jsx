import {
  Box,
  Icon<PERSON>utton,
  <PERSON><PERSON><PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import { Download, Repeat, Upload } from '@mui/icons-material';
import { dayjs } from '@core/../utils/dateUtils';
import { downloadCSV } from '../utils/data';
import { useState } from 'react';

export default function UtilityButtons({ forecast, selectedRevenueGroups, data }) {
  const week = dayjs(forecast.periodStart).week();
  const [openDialog, setOpenDialog] = useState(false);
  const [openUploadDialog, setOpenUploadDialog] = useState(false);

  const handleRerunOpen = () => {
    setOpenDialog(true);
  };

  const handleRerunClose = () => {
    setOpenDialog(false);
  };

  const handleRerunConfirm = () => {
    handleRerunClose();
  };

  const handleUploadOpen = () => {
    setOpenUploadDialog(true);
  };

  const handleUploadClose = () => {
    setOpenUploadDialog(false);
  };

  const handleUploadConfirm = () => {
    handleUploadClose();
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ mr: 2 }}>
        <Box sx={{ fontSize: '0.7rem', color: 'gray' }}>
          Last run: {dayjs(forecast?.runDate).format('YYYY-MM-DD HH:mm:ss')}
        </Box>
        <Box sx={{ fontSize: '0.7rem', color: 'gray' }}>
          Next run: {dayjs(forecast?.nextRunDate).format('YYYY-MM-DD HH:mm:ss')}
        </Box>
      </Box>
      <Box>
        <Tooltip title="Download CSV">
          <IconButton size="large" onClick={() => downloadCSV(data, selectedRevenueGroups, week)}>
            <Download />
          </IconButton>
        </Tooltip>
        <Tooltip title="Rerun Forecasting Agent">
          <IconButton size="large" onClick={handleRerunOpen}>
            <Repeat />
          </IconButton>
        </Tooltip>
        <Tooltip title="Upload a new Revenue Plan">
          <IconButton size="large" onClick={handleUploadOpen}>
            <Upload />
          </IconButton>
        </Tooltip>
      </Box>

      <Dialog open={openDialog} onClose={handleRerunClose}>
        <DialogTitle>Confirm Rerun</DialogTitle>
        <DialogContent>
          Have you uploaded a new Revenue Plan? If not, the results will be the same as before.
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRerunClose} autoFocus>
            Cancel
          </Button>
          <Button onClick={handleRerunConfirm} disabled>
            Coming soon
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={openUploadDialog} onClose={handleUploadClose} maxWidth="sm" fullWidth>
        <DialogTitle>Upload Revenue Plan</DialogTitle>
        <DialogContent>
          <Box
            component="label"
            sx={{
              border: '2px dashed #ccc',
              borderRadius: '8px',
              p: 3,
              textAlign: 'center',
              bgcolor: '#fafafa',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: '#f0f0f0',
                borderColor: '#999',
              },
              width: '100%',
              boxSizing: 'border-box',
              margin: '8px 0',
              display: 'block', // Add this to maintain block behavior
            }}
          >
            <input
              type="file"
              accept=".csv,.xlsx,.xls"
              style={{ display: 'none' }} // Changed from hidden to style
              onChange={e => {
                // Handle file selection here
                console.log(e.target.files[0]);
              }}
            />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 2,
                width: '100%',
              }}
            >
              <Upload sx={{ fontSize: 48, color: '#666' }} />
              <Box>
                <Box sx={{ mb: 1 }}>Drag and drop your file here</Box>
                <Box sx={{ color: '#666', fontSize: '0.875rem' }}>or click to select a file</Box>
                <Box sx={{ color: '#666', fontSize: '0.875rem', mt: 1 }}>
                  Supported formats: CSV, Excel
                </Box>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleUploadClose}>Cancel</Button>
          <Button onClick={handleUploadConfirm} disabled>
            Coming soon
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
