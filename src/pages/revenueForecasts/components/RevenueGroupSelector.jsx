import { Autocomplete, FormControl, TextField } from '@mui/material';

export default function RevenueGroupSelector({
  revenueGroups,
  selectedRevenueGroups,
  setSelectedRevenueGroups,
}) {
  return (
    <FormControl sx={{ flex: 1 }}>
      <Autocomplete
        onChange={(_e, val) => setSelectedRevenueGroups(val)}
        multiple
        options={revenueGroups}
        filterSelectedOptions
        getOptionLabel={option => option}
        value={selectedRevenueGroups}
        renderInput={params => <TextField {...params} label="Revenue Groups" variant="outlined" />}
      />
    </FormControl>
  );
}
