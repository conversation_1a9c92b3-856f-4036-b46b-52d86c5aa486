import { dayjs } from '@core/../utils/dateUtils';
import { memoize } from 'lodash';

export const convertForecastToRows = memoize(
  (forecast, selectedRevenueGroups) =>
    [...Array(7)].map((_, i) => {
      const date = dayjs(forecast?.periodStart).add(i, 'day');
      const displayDate = date.locale('en').format('MMM D, YYYY');
      const revenueGroupDate = date.format('YYYY-MM-DD');
      const intlFormatter = num =>
        forecast?.keyMetric.symbol + new Intl.NumberFormat('en-GB').format(num);

      return {
        date: displayDate,
        ...selectedRevenueGroups.reduce((acc, group) => {
          const forecastForDateAndGroup = forecast?.revenueGroups
            .find(x => x.name === group)
            .forecast.find(x => x.date === revenueGroupDate);
          const displayValue =
            `${intlFormatter(forecastForDateAndGroup?.revenue)} ` +
            `(${Math.round(forecastForDateAndGroup?.contribution * 100)}%)`;

          return {
            ...acc,
            [group]: displayValue,
          };
        }, {}),
      };
    }),
  (forecast, selectedRevenueGroups) => forecast?.id + selectedRevenueGroups,
);

export const downloadCSV = (data, selectedRevenueGroups, week) => {
  const headers = ['Date', ...selectedRevenueGroups].map(header => `"${header}"`);
  const csvRows = [headers];

  data.forEach(row => {
    const rowData = [`"${row.date}"`];
    selectedRevenueGroups.forEach(group => {
      rowData.push(`"${row[group]}"`);
    });
    csvRows.push(rowData);
  });

  const csvContent = csvRows.map(row => row.join(',')).join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.setAttribute('href', url);
  link.setAttribute('download', `forecast_data_week${week}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
