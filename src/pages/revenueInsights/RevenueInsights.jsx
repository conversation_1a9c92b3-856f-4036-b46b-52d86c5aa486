import { useEffect, useState, useContext } from 'react';
import { CardContent } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';

import { State } from '@core/providers/State';

import useInsights from './hooks/useInsights';
import useQuery from '@core/hooks/useQuery';

import InsightsGrid from './components/revenueInsights/InsightsGrid';
import PaginationControl from './components/revenueInsights/PaginationControl';
import InputBar from './components/revenueInsights/InputBar';
import PageContainer from '@core/layout/PageContainer';
import PageTitle from '@core/layout/PageTitle';

const ITEMS_PER_PAGE = 20;

function RevenueInsights() {
  const { updateState } = useContext(State);
  const navigate = useNavigate();
  const location = useLocation();
  const query = useQuery();

  // Preserve the filtered count for pagination between data fetches
  const [filteredInsightsCount, setFilteredInsightsCount] = useState();

  // Get url query params
  let page = query.get('page');
  page = page ? parseInt(page) : 1;
  let sensitivity = query.get('sensitivity');
  sensitivity ||= 'medium';

  // Filters
  const handleChangePage = (_e, newPage) =>
    navigate(`${location.pathname}?page=${newPage + 1}&sensitivity=${sensitivity}`);
  const handleChangeSensitivity = (_e, newSensitivity) =>
    navigate(`${location.pathname}?sensitivity=${newSensitivity}`);

  // fetch insights
  const { insights, filteredCount, error, isLoading } = useInsights(page, sensitivity);

  useEffect(() => {
    if (filteredCount >= 0) setFilteredInsightsCount(filteredCount);
  });

  useEffect(() => {
    if (error) {
      console.log(error);
      updateState({ httpErrorNotifVisible: true });
    }
  }, [error]);

  const PaginationControlInstance = () => {
    const showPagination = isLoading || insights?.length > 0;
    if (!showPagination) return;

    return (
      <PaginationControl
        count={filteredInsightsCount}
        page={page - 1}
        handleChangePage={handleChangePage}
        disabled={isLoading}
        itemsPerPage={ITEMS_PER_PAGE}
      />
    );
  };

  return (
    <PageContainer>
      <PageTitle title="Revenue Insights" />
      <InputBar
        sensitivity={sensitivity}
        handleChangeSensitivity={handleChangeSensitivity}
        setInsightsCount={setFilteredInsightsCount}
      />
      <CardContent sx={{ padding: '0 24px 24px 24px' }}>
        <PaginationControlInstance />
        <InsightsGrid insights={insights} isLoading={isLoading} itemsPerPage={ITEMS_PER_PAGE} />
        <PaginationControlInstance />
      </CardContent>
    </PageContainer>
  );
}

export default RevenueInsights;
