import { useContext } from 'react';
import { State } from '@core/providers/State';
import useSWR from 'swr';

const useSearch = (query, page) => {
  const { state } = useContext(State);

  const url =
    import.meta.env.VITE_INSIGHTS_APP_BFF_API_URL +
    `/api/v1/organizations/${state.auth0OrgID}/insights/search?q=${query}&page=${page}`;

  const { data, error, isLoading } = useSWR(url, { shouldRetryOnError: false });

  return {
    insights: data?.insights,
    filteredCount: data?.filteredCount,
    error,
    isLoading,
  };
};

export default useSearch;
