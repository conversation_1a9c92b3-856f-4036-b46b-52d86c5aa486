import React from 'react';
import useSearch from './useSearch';
import searchFixture from '../../../../cypress/fixtures/search.json';

const ORG_ID = searchFixture.insights[0].orgId;

// Test component that uses the useSearch hook
const TestComponent = ({ query, page }) => {
  const { insights, filteredCount, error, isLoading } = useSearch(query, page);

  if (isLoading) return <div data-testid="loading">Loading...</div>;
  if (error) return <div data-testid="error">Error: {error.message}</div>;

  return (
    <div data-testid="search-results">
      <div data-testid="total-count">{filteredCount}</div>
      {insights.map((insight, index) => (
        <div key={index} data-testid={`incident-${index}`}>
          {Object.entries(insight).map(([key, value]) => (
            <div key={key} data-testid={`${index}-${key}`}>
              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

const compareInsights = insights => {
  insights.forEach((insight, index) => {
    Object.entries(insight).forEach(([key, value]) => {
      //console.log('xxx', key, value);
      cy.get(`[data-testid="${index}-${key}"]`, { log: false }).should('exist');
      if (value === null) {
        cy.get(`[data-testid="${index}-${key}"]`, { log: false }).should('have.text', 'null');
      } else if (typeof value === 'object') {
        cy.get(`[data-testid="${index}-${key}"]`, { log: false }).should(
          'contain',
          JSON.stringify(value),
        );
      } else if (typeof value === 'boolean') {
        cy.get(`[data-testid="${index}-${key}"]`, { log: false }).should(
          'have.text',
          String(value),
        );
      }
    });
  });
};

describe('useSearch Hook', () => {
  beforeEach(() => {
    cy.intercept('GET', '**/api/v1/organizations/*/insights/search?q=*&page=*', {
      fixture: 'search.json',
    }).as('getSearchResults');
  });

  it('should fetch and display all enriched search results', () => {
    cy.mountWithSWR(<TestComponent query="test" page={1} />);

    cy.get('[data-testid="loading"]').should('be.visible');
    cy.wait('@getSearchResults');
    cy.get('[data-testid="loading"]').should('not.exist');
    cy.get('[data-testid="search-results"]').should('be.visible');

    compareInsights(searchFixture.insights);
  });

  it('should handle errors', () => {
    cy.intercept('GET', '**/api/v1/organizations/*/insights/search?q=*&page=*', {
      statusCode: 500,
      body: 'Server Error',
    }).as('getSearchError');

    cy.mountWithSWR(<TestComponent query="test" page={1} />);

    cy.wait('@getSearchError');
    cy.get('[data-testid="error"]').should('be.visible');
  });

  it('should use the correct API URL', () => {
    const query = 'test';
    const page = 1;
    const expectedUrl = `${Cypress.env('VITE_INSIGHTS_APP_BFF_API_URL')}/api/v1/organizations/${ORG_ID}/insights/search?q=${query}&page=${page}`;

    cy.intercept('GET', expectedUrl).as('getSearchResults');

    cy.mountWithSWR(<TestComponent query={query} page={page} />);

    cy.wait('@getSearchResults').then(interception => {
      expect(interception.request.url).to.equal(expectedUrl);
    });
  });
});
