import { useEffect, useContext, useState } from 'react';
import { CardContent, Grid, Box, CircularProgress } from '@mui/material';

import { State } from '@core/providers/State';
import useQuery from '@core/hooks/useQuery';

import InsightsGrid from './components/revenueInsights/InsightsGrid';
import InputBar from './components/revenueInsights/InputBar';
import useSearch from './hooks/useSearch';
import PaginationControl from './components/revenueInsights/PaginationControl';
import { useLocation, useNavigate } from 'react-router-dom';
import PageContainer from '@core/layout/PageContainer';
import PageTitle from '@core/layout/PageTitle';

const ITEMS_PER_PAGE = 20;

function NoResults() {
  return (
    <Grid item xs={12}>
      <Box color="rgba(0, 0, 0, 0.60)">No results - refine your search query and try again</Box>
    </Grid>
  );
}

function LoadingTxt() {
  return (
    <Grid item xs={12}>
      <Box
        color="rgba(0, 0, 0, 0.60)"
        sx={{ m: '20px 0', display: 'flex', gap: '10px', alignItems: 'center' }}
      >
        <CircularProgress size="30px" />
        <Box sx={{ width: { xs: '100%' } }}>
          Your search is in progress! Our AI-powered engine is diligently analyzing data to deliver
          the best results to you promptly.
        </Box>
      </Box>
    </Grid>
  );
}

function RevenueInsightsSearch() {
  const { updateState } = useContext(State);
  const navigate = useNavigate();
  const query = useQuery();
  const location = useLocation();

  // preserve the filtered count for pagination between data fetches
  const [incidentsCount, setIncidentsCount] = useState();

  const userQuery = query.get('query');
  const page = query.get('page') ? parseInt(query.get('page')) : 1;

  const handleChangePage = (_e, newPage) => {
    const searchParams = new URLSearchParams();
    searchParams.set('query', userQuery);
    searchParams.set('page', newPage + 1);
    navigate(`${location.pathname}?${searchParams.toString()}`);
  };

  const { insights, filteredCount, error, isLoading } = useSearch(userQuery, page);

  useEffect(() => {
    if (filteredCount >= -1) setIncidentsCount(filteredCount);
  });

  useEffect(() => {
    if (error) {
      console.log(error);
      updateState({ httpErrorNotifVisible: true });
    }
  }, [error]);

  const PaginationControlInstance = () => {
    const showPagination = isLoading || insights?.length > 0;
    if (!showPagination) return;

    return (
      <PaginationControl
        count={incidentsCount}
        page={page - 1}
        handleChangePage={handleChangePage}
        disabled={isLoading}
        itemsPerPage={ITEMS_PER_PAGE}
      />
    );
  };

  //const showPagination = insights?.length > 0;

  return (
    <PageContainer>
      <PageTitle title="Revenue Insights" />
      <InputBar sensitivity={'medium'} setIncidentsCount={setIncidentsCount} />
      <CardContent sx={{ padding: '0 24px 24px 24px' }}>
        {insights?.length === 0 && !isLoading && <NoResults />}
        {isLoading && <LoadingTxt />}
        <PaginationControlInstance />
        <InsightsGrid insights={insights} isLoading={isLoading} />
        <PaginationControlInstance />
      </CardContent>
    </PageContainer>
  );
}

export default RevenueInsightsSearch;
