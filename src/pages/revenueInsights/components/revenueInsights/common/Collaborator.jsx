import { useState } from 'react';
import { Avatar, Popover, Typography } from '@mui/material';

const cfl = str => str?.charAt(0)?.toUpperCase() + str?.slice(1);

function Collaborator({ collaborator }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handlePopoverOpen = event => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Avatar onMouseEnter={handlePopoverOpen} onMouseLeave={handlePopoverClose}>
        {collaborator === 'Unassigned' ? '?' : cfl(collaborator)[0]}
      </Avatar>
      <Popover
        id="mouse-over-popover"
        sx={{
          pointerEvents: 'none',
        }}
        open={open}
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        onClose={handlePopoverClose}
        disableRestoreFocus
      >
        <Typography sx={{ p: 1 }}>{collaborator}</Typography>
      </Popover>
    </>
  );
}

export default Collaborator;
