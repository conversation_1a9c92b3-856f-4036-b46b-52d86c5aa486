import React from 'react';
import HypothesesTestedWidget from './HypothesesTestedWidget';

describe('HypothesesTestedWidget Component', () => {
  const mockHypotheses = [
    { id: 'a', statement: 'Hypothesis 1', verdict: 'inconclusive' },
    { id: 'b', statement: 'Hypothesis 2', verdict: 'retained' },
    { id: 'c', statement: 'Hypothesis 3', verdict: 'rejected' },
    { id: 'd', statement: 'Hypothesis 4', verdict: 'untested' },
    { id: 'e', statement: 'Hypothesis 5', verdict: 'unknown' },
    { id: 'f', statement: 'Hypothesis 6', verdict: 'skipped' },
  ];

  beforeEach(() => {
    cy.mountWithSWR(<HypothesesTestedWidget insightId="123" hypotheses={mockHypotheses} />);
  });

  it('displays all hypotheses in the correct order with correct icons', () => {
    cy.contains('HYPOTHESES TESTED').should('exist');

    const expectedHypotheses = [
      { text: 'Hypothesis 2', icon: 'retained-icon' },
      { text: 'Hypothesis 3', icon: 'rejected-icon' },
      { text: 'Hypothesis 1', icon: 'inconclusive-icon' },
      { text: 'Hypothesis 4', icon: 'untested-icon' },
      { text: 'Hypothesis 5', icon: null },
      { text: 'Hypothesis 6', icon: 'skipped-icon' },
    ];

    cy.get('[data-testid^="hypothesis-item-"]')
      .should('have.length', expectedHypotheses.length)
      .each(($el, index) => {
        const hypothesis = expectedHypotheses[index];
        cy.wrap($el).should('contain', hypothesis.text);

        if (hypothesis.icon) {
          cy.wrap($el).find(`[data-testid="${hypothesis.icon}"]`).should('exist');
        } else {
          cy.wrap($el)
            .find(
              '[data-testid^="retained-icon"], [data-testid^="rejected-icon"], [data-testid^="inconclusive-icon"], [data-testid^="skipped-icon"], [data-testid^="untested-icon"]',
            )
            .should('not.exist');
        }
      });
  });

  it('opens HypothesesModal when a hypothesis is clicked', () => {
    cy.intercept(
      'POST',
      `${Cypress.env('VITE_INSIGHTS_APP_BFF_API_URL')}/insights/*/user_interactions/hypothesis_click`,
      req => {
        expect(req.body).to.deep.equal({
          actionTargetId: mockHypotheses[0].id,
          actionValue: mockHypotheses[0].statement,
        });
        req.reply({
          statusCode: 200,
          body: {},
        });
      },
    ).as('postUserInteraction');

    cy.get('[data-testid="hypotheses-modal"]').should('not.exist');
    cy.contains('Hypothesis 1').click();
    cy.get('[data-testid="hypotheses-modal"]').should('exist');
  });

  it('allows adding a new hypothesis', () => {
    cy.intercept(
      'POST',
      `${Cypress.env('VITE_INSIGHTS_APP_BFF_API_URL')}/insights/*/user_interactions/hypothesis_feedback`,
      req => {
        expect(req.body).to.deep.equal({
          actionValue: 'New test hypothesis',
        });
        req.reply({
          statusCode: 200,
          body: {},
        });
      },
    ).as('postUserInteraction');

    const newHypothesis = 'New test hypothesis';
    cy.get('[id="question"]').type(newHypothesis, { delay: 0 });

    cy.contains('Feedback submitted').should('not.exist');

    cy.get('button[aria-label="send"]').click();
    cy.wait('@postUserInteraction');

    cy.contains('Feedback submitted').should('be.visible');
  });

  it('displays error notification when adding a hypothesis fails', () => {
    cy.on('uncaught:exception', () => false);

    cy.intercept(
      'POST',
      `${Cypress.env('VITE_INSIGHTS_APP_BFF_API_URL')}/insights/*/user_interactions/hypothesis_feedback`,
      {
        statusCode: 500,
        body: JSON.stringify({ error: 'Internal Server Error' }),
      },
    ).as('postUserInteraction');

    const newHypothesis = 'New test hypothesis';
    cy.get('[id="question"]').type(newHypothesis, { delay: 0 });
    cy.get('button[aria-label="send"]').click();

    cy.wait('@postUserInteraction');
    cy.contains('There was an error submitting feedback').should('be.visible');
  });

  it('renders with no hypotheses', () => {
    cy.mountWithSWR(<HypothesesTestedWidget insightId="123" hypotheses={[]} />);

    cy.get('[data-testid^="hypothesis-item-"]').should('not.exist');
  });
});
