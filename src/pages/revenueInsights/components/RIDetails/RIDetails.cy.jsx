import React from 'react';
import RIDetails from '../../RIDetails';

describe('RIDetails Component', () => {
  beforeEach(() => {
    cy.viewport(1280, 720);
    cy.intercept('GET', '**/insights/*', { fixture: 'incident.json' }).as('getInsight');
  });

  it('renders all widgets in the left column - User', () => {
    cy.mountWithSWR(<RIDetails />, { userRoles: ['Insights App User'] });

    cy.wait('@getInsight');
    cy.get('[data-testid="wrong-org-alert"]').should('not.exist');
    cy.get('[data-testid="riDetails-edit"]').should('not.exist');
    cy.get('[data-testid="ri-details-main"]').should('exist');
  });

  it('renders all widgets in the left column - Admin', () => {
    cy.mountWithSWR(<RIDetails />, { userRoles: ['Insights App Admin'] });

    cy.wait('@getInsight');
    cy.get('[data-testid="wrong-org-alert"]').should('not.exist');
    cy.get('[data-testid="riDetails-edit"]').should('exist');
    cy.get('[data-testid="ri-details-main"]').should('exist');
  });

  it('renders all widgets in the right column', () => {
    cy.mountWithSWR(<RIDetails />, { userRoles: ['Insights App Admin'] });

    cy.wait('@getInsight');
    cy.get('[data-testid="ai-suggestion-widget"]').should('exist');
    cy.get('[data-testid="hypotheses-tested-widget"]').should('exist');
    cy.get('[data-testid="chat-with-your-data-widget"]').should('exist');
  });

  it('displays skeleton loading when no incident data is available', () => {
    // Fix flakiness by overriding beforeEach intercept to add a delay
    cy.intercept('GET', '**/insights/*', () => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({ fixture: 'incident.json' });
        }, 1000);
      });
    }).as('getInsightWithDelay');

    cy.mountWithSWR(<RIDetails />, { userRoles: ['Insights App Admin'] });

    cy.get('[data-testid="ri-details-skeleton"]').should('exist');
    cy.wait('@getInsightWithDelay');
    cy.get('[data-testid="ri-details-skeleton"]').should('not.exist');
  });

  it('opens EditIncidentModal when Edit button is clicked', () => {
    cy.mountWithSWR(<RIDetails />, { userRoles: ['Insights App Admin'] });

    cy.wait('@getInsight');
    cy.get('[data-testid="edit-incident-modal"]').should('not.exist');
    cy.get('[data-testid="riDetails-edit"]').click();
    cy.get('[data-testid="edit-incident-modal"]').should('be.visible');
  });

  it('displays WrongOrgAlert with correct org name when incident does not belong to current organization and user has access to it', () => {
    cy.intercept('GET', '**/insights/*', {
      statusCode: 409,
      body: { correctOrg: { display_name: 'xyz' } },
    }).as('getInsight');

    cy.mountWithSWR(<RIDetails />, {
      userRoles: ['Insights App Admin'],
    });

    cy.wait('@getInsight');
    cy.get('[data-testid="wrong-org-alert"]').should('exist');
    cy.contains('xyz').should('exist');

    cy.get('[data-testid="CloseIcon"]').click();
    cy.get('[data-testid="wrong-org-alert"]').should('not.exist');
  });

  it('displays error notification when incident does not belong to current organization and user does not have access to it', () => {
    cy.intercept('GET', '**/insights/*', {
      statusCode: 404,
      body: {},
    }).as('getInsight');

    cy.mountWithSWR(<RIDetails />, {
      userRoles: ['Insights App Admin'],
    });

    cy.wait('@getInsight');
    cy.get('[data-testid="global-notification"]').should('be.visible');
    cy.get('.MuiAlert-message').should('have.text', 'Insight not found');
  });
});
