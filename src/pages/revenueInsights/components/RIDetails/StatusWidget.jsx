import React from 'react';
import { <PERSON>, Stack, Card<PERSON>ontent, CardHeader } from '@mui/material';

import RICardStatus from '../riCard/RICardStatus';
import Collaborator from '../common/Collaborator';
import Widget from '../../riDetails/Widget';

const cfl = str => str?.charAt(0)?.toUpperCase() + str?.slice(1);

const cardSx = {
  border: 0,
  '@media (max-width: 1200px)': {
    width: '50%',
  },
  '@media (min-width: 1536px)': {
    width: '50%',
  },
};

const cardHeaderSx = {
  '& .MuiCardHeader-title': {
    fontSize: 18,
    fontFamily: 'Roboto',
    fontWeight: '700',
    color: 'rgba(0, 0, 0, 0.60)',
  },
};

const StatusWidget = ({ issueStatus, assignedResponsibility }) => {
  assignedResponsibility ||= 'Unassigned';

  const Content = () => (
    <>
      <Card variant="outlined" sx={cardSx}>
        <CardHeader title="Status" sx={cardHeaderSx} />
        <CardContent>
          <Stack direction="column" spacing={1} alignItems="flex-start">
            <RICardStatus label={cfl(issueStatus)} />
          </Stack>
        </CardContent>
      </Card>
      <Card variant="outlined" sx={cardSx}>
        <CardHeader title="Collaborators" sx={cardHeaderSx} />
        <CardContent>
          <Collaborator collaborator={assignedResponsibility} />
        </CardContent>
      </Card>
    </>
  );

  return (
    <Widget>
      <Stack direction="row" sx={{ display: { lg: 'none', xl: 'flex' } }}>
        <Content />
      </Stack>
      <Stack direction="column" sx={{ display: { xs: 'none', lg: 'flex', xl: 'none' } }}>
        <Content />
      </Stack>
    </Widget>
  );
};

export default StatusWidget;
