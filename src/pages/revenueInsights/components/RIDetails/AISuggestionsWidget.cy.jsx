import React from 'react';
import AISuggestionsWidget from './AISuggestionsWidget';

describe('AISuggestionsWidget Component', () => {
  const mockSuggestions = {
    immediate: [
      {
        id: 1,
        action: 'Analyze “Direct” channel traffic sources',
      },
      {
        id: 2,
        action: 'Review content performance of top products',
      },
    ],
    long_term: [
      {
        id: 1,
        action: 'Optimize marketing strategies for “Direct” channel',
      },
      {
        id: 2,
        action: 'Optimize stock levels for top products',
      },
    ],
  };

  beforeEach(() => {
    cy.intercept('GET', '**/insights/*/user_interactions/ai_suggestions', {
      fixture: 'user_interactions.json',
    }).as('getUserInteractions');

    cy.mountWithSWR(
      <AISuggestionsWidget
        actions={mockSuggestions}
        incidentId="123"
        lv3AiSuggestions="Some random text"
      />,
    );
  });

  it('displays the AI suggestions', () => {
    cy.get('[data-testid="suggestion-title"]').should('exist');
    cy.get('[data-testid="suggestion-alert"]').should('exist');
    cy.contains('Immediate Actions').should('exist');
    cy.contains('Longer-Term Solutions').should('exist');
    cy.get('[data-testid^="immediate-suggestions"] [data-testid="suggestion-item"]').should(
      'have.length',
      2,
    );
    cy.get('[data-testid^="longer-term-suggestions"] [data-testid="suggestion-item"]').should(
      'have.length',
      2,
    );
    cy.get('[data-testid="suggestion-actions"] [data-testid="like-button"]').should(
      'have.length',
      4,
    );

    cy.get('[data-testid="suggestion-actions"] [data-testid="dislike-button"]').should(
      'have.length',
      4,
    );

    cy.wait('@getUserInteractions');
    cy.get('[data-testid="suggestion-actions"]')
      .eq(0)
      .find('[data-testid="like-badge"]')
      .should('contain', 3);
    cy.get('[data-testid="suggestion-actions"]')
      .eq(0)
      .find('[data-testid="dislike-badge"]')
      .should('contain', 5);
    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .should('not.have.nested.property', '[data-testid="like-badge"]');
    cy.get('[data-testid="suggestion-actions"]')
      .eq(2)
      .find('[data-testid="like-badge"]')
      .should('contain', 6);
    cy.get('[data-testid="suggestion-actions"]')
      .eq(3)
      .find('[data-testid="dislike-badge"]')
      .should('contain', 3);
  });

  it('allows to like a suggestion', () => {
    cy.intercept('POST', '**/insights/*/user_interactions/ai_suggestions', req => {
      expect(req.body).to.include({
        actionValue: 'like',
        actionTargetId: 2,
        actionTargetGroup: 'immediate',
      });

      req.reply({
        statusCode: 200,
        body: { likes: 8, dislikes: 1, userInteraction: 'like' },
      });
    }).as('likeSuggestion');

    cy.contains('Feedback submitted').should('not.exist');

    cy.get('[data-testid="suggestion-actions"]').eq(1).find('[data-testid="like-button"]').click();
    cy.wait('@likeSuggestion');

    cy.contains('Feedback submitted').should('be.visible');

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .find('[data-testid="like-badge"]')
      .should('contain', 8);

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .find('[data-testid="dislike-badge"]')
      .should('contain', 1);

    cy.get('[data-testid="suggestion-actions"]')
      .eq(0)
      .find('[data-testid="like-badge"]')
      .should('contain', 3);
    cy.get('[data-testid="suggestion-actions"]')
      .eq(0)
      .find('[data-testid="dislike-badge"]')
      .should('contain', 5);
  });

  it('allows to dislike a suggestion', () => {
    cy.intercept('POST', '**/insights/*/user_interactions/ai_suggestions', req => {
      expect(req.body).to.include({
        actionValue: 'dislike',
        actionTargetId: 2,
        actionTargetGroup: 'immediate',
      });

      req.reply({
        statusCode: 200,
        body: { likes: 7, dislikes: 3, userInteraction: 'dislike' },
      });
    }).as('dislikeSuggestion');

    cy.contains('Feedback submitted').should('not.exist');

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .find('[data-testid="dislike-button"]')
      .click();
    cy.wait('@dislikeSuggestion');

    cy.contains('Feedback submitted').should('be.visible');

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .find('[data-testid="like-badge"]')
      .should('contain', 7);

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .find('[data-testid="dislike-badge"]')
      .should('contain', 3);
  });

  it('displays error notification when like fails', () => {
    cy.intercept('POST', '**/insights/*/user_interactions/ai_suggestions', {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal Server Error' }),
    }).as('postEvent');

    cy.get('[data-testid="suggestion-actions"]').eq(1).find('[data-testid="like-button"]').click();
    cy.wait('@postEvent');
    cy.contains('There was an error submitting feedback').should('be.visible');
    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .should('not.have.nested.property', '[data-testid="like-badge"]');
  });

  it('displays error notification when dislike fails', () => {
    cy.intercept('POST', '**/insights/*/user_interactions/ai_suggestions', {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal Server Error' }),
    }).as('postEvent');

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .find('[data-testid="dislike-button"]')
      .click();
    cy.wait('@postEvent');
    cy.contains('There was an error submitting feedback').should('be.visible');

    cy.get('[data-testid="suggestion-actions"]')
      .eq(1)
      .should('not.have.nested.property', '[data-testid="like-badge"]');
  });

  it('renders with no suggestions', () => {
    cy.mountWithSWR(<AISuggestionsWidget actions={{ immediate: [], long_term: [] }} />);
    cy.get('[data-testid="suggestion-title"]').should('exist');
    cy.get('[data-testid="suggestion-alert"]').should('exist');
    cy.contains('Immediate Actions').should('exist');
    cy.contains('Longer-Term Solutions').should('exist');
    cy.get('[data-testid^="immediate-suggestions"] li').should('have.length', 0);
    cy.get('[data-testid^="longer-term-suggestions"] li').should('have.length', 0);
  });
});
