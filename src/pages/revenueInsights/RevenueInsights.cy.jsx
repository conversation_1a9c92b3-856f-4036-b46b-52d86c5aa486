import React from 'react';
import RevenueInsights from './RevenueInsights';

const ITEMS_PER_PAGE = 20;

describe('RevenueInsights Component', () => {
  beforeEach(() => {
    cy.viewport(1280, 720);
    cy.intercept('GET', '**/organizations/*/insights*', {
      fixture: 'detailed_incidents.json',
    }).as('getInsights');
  });

  it('renders the component with default settings', () => {
    cy.mountWithSWR(<RevenueInsights />, { userRoles: ['Insights App User'] });

    cy.wait('@getInsights').its('request.url').should('include', 'page=1');
    cy.get('[data-testid="title-bar"]').should('exist');
    cy.get('[data-testid="input-bar"]').should('exist');
    cy.get('[data-testid="incidents-grid"]').should('exist');
    cy.get('[data-testid="pagination-control"]').should('exist');
  });

  it('displays error notification when API call fails', () => {
    cy.intercept('GET', '**/organizations/*/insights*', {
      statusCode: 500,
    }).as('getIncidentsError');

    cy.mountWithSWR(<RevenueInsights />);

    cy.wait('@getIncidentsError');
    cy.get('@updateState').should('have.been.calledWith', { httpErrorNotifVisible: true });
  });

  describe('Sensitivity functionality', () => {
    beforeEach(() => {
      cy.mountWithSWR(<RevenueInsights />);
      cy.wait('@getInsights');
    });

    it('should have default sensitivity set to Medium', () => {
      cy.get('@getInsights')
        .its('request.url')
        .should('include', 'sensitivity=low,medium')
        .and('not.include', 'sensitivity=high');

      cy.get('[data-testid="sensitivity-toggle"] input').should('have.value', '1');
    });

    it('changes sensitivity to Low and updates URL', () => {
      cy.contains('span .MuiSlider-markLabel', 'Low').click();
      cy.get('[data-testid="sensitivity-toggle"] input').should('have.value', '0');
      cy.wait('@getInsights')
        .its('request.url')
        .should('include', 'sensitivity=low')
        .and('not.include', '&sensitivity=medium');

      cy.location('search').should('eq', '?sensitivity=low');
    });

    it('uses cache when changing sensitivity from Low back to Medium', () => {
      cy.contains('span .MuiSlider-markLabel', 'Low').click();
      cy.contains('span .MuiSlider-markLabel', 'Medium').click();

      cy.get('[data-testid="sensitivity-toggle"] input').should('have.value', '1');
      cy.get('@getInsights.all').should('have.length', 2);
      cy.location('search').should('eq', '?sensitivity=medium');
    });

    it('changes sensitivity to High and updates URL', () => {
      cy.contains('span .MuiSlider-markLabel', 'High').click();
      cy.get('[data-testid="sensitivity-toggle"] input').should('have.value', '2');
      cy.wait('@getInsights')
        .its('request.url')
        .should('not.include', 'sensitivity=low')
        .and('not.include', '&sensitivity=medium')
        .and('not.include', '&sensitivity=high');

      cy.location('search').should('eq', '?sensitivity=high');
    });
  });

  describe('Pagination functionality', () => {
    beforeEach(() => {
      cy.fixture('detailed_incidents.json').as('incidentsData');
    });

    it('handles case with 0 incidents', () => {
      cy.intercept('GET', '**/organizations/*/insights*', {
        body: { insights: [], filteredCount: 0 },
      }).as('getEmptyIncidents');

      cy.mountWithSWR(<RevenueInsights />);

      cy.wait('@getEmptyIncidents');
      cy.get('[data-testid="pagination-control"]').should('not.exist');
    });

    it('navigates through pages and uses cache correctly', () => {
      const totalIncidents = 100;
      cy.get('@incidentsData').then(incidentsData => {
        const modifiedData = {
          ...incidentsData,
          insights: Array(totalIncidents)
            .fill(incidentsData.insights[0])
            .map((incident, index) => ({
              ...incident,
              title: `${incident.title} - ${index + 1}`,
            })),
          filteredCount: totalIncidents,
        };

        cy.intercept('GET', '**/organizations/*/insights*', req => {
          const page = parseInt(new URLSearchParams(req.url.split('?')[1]).get('page')) || 1;
          const start = (page - 1) * ITEMS_PER_PAGE;
          const end = start + ITEMS_PER_PAGE;
          req.reply({
            body: {
              ...modifiedData,
              insights: modifiedData.insights.slice(start, end),
            },
          });
        }).as('getPaginatedIncidents');

        cy.mountWithSWR(<RevenueInsights />);

        // Check first page
        cy.wait('@getPaginatedIncidents').its('request.url').should('include', 'page=1');
        cy.checkPaginationAndIncidents(1, totalIncidents, 'Dresses - 1', 'Dresses - 20');

        // Navigate to second page
        cy.get('[data-testid="pagination-control"] button[aria-label="Go to next page"]')
          .first()
          .click();
        cy.wait('@getPaginatedIncidents').its('request.url').should('include', 'page=2');
        cy.checkPaginationAndIncidents(2, totalIncidents, 'Dresses - 21', 'Dresses - 40');

        // Navigate to third page
        cy.get('[data-testid="pagination-control"] button[aria-label="Go to next page"]')
          .first()
          .click();
        cy.wait('@getPaginatedIncidents').its('request.url').should('include', 'page=3');
        cy.checkPaginationAndIncidents(3, totalIncidents, 'Dresses - 41', 'Dresses - 60');

        // Navigate back to second page (should use cache)
        cy.get('[data-testid="pagination-control"] button[aria-label="Go to previous page"]')
          .first()
          .click();
        cy.get('@getPaginatedIncidents.all').should('have.length', 3);
        cy.checkPaginationAndIncidents(2, totalIncidents, 'Dresses - 21', 'Dresses - 40');

        // Navigate back to third page (should use cache)
        cy.get('[data-testid="pagination-control"] button[aria-label="Go to next page"]')
          .first()
          .click();
        cy.get('@getPaginatedIncidents.all').should('have.length', 3);
        cy.checkPaginationAndIncidents(3, totalIncidents, 'Dresses - 41', 'Dresses - 60');
      });
    });
  });
});
