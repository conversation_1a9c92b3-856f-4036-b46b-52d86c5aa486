import { describe, it, expect } from 'vitest';
import { removeConsecutiveDuplicates } from './utils.js';

describe('removeConsecutiveDuplicates', () => {
  it('should return empty array when input is empty', () => {
    const result = removeConsecutiveDuplicates([]);
    expect(result).toEqual([]);
  });

  it('should return single item unchanged when array has one element', () => {
    const input = [{ display_timeline: true }];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: true }]);
  });

  it('should return single item unchanged when array has one element with display_timeline false', () => {
    const input = [{ display_timeline: false }];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: false }]);
  });

  it('should not modify array when all items have display_timeline true', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: true, name: 'item2' },
      { display_timeline: true, name: 'item3' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([
      { display_timeline: true, name: 'item1' },
      { display_timeline: true, name: 'item2' },
      { display_timeline: true, name: 'item3' },
    ]);
  });

  it('should remove single item with display_timeline false and increment previous duration', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: false, name: 'item2' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: true, name: 'item1', duration: 2 }]);
  });

  it('should remove multiple consecutive items with display_timeline false', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: false, name: 'item2' },
      { display_timeline: false, name: 'item3' },
      { display_timeline: false, name: 'item4' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: true, name: 'item1', duration: 4 }]);
  });

  it('should handle mixed pattern correctly', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: false, name: 'item2' },
      { display_timeline: true, name: 'item3' },
      { display_timeline: false, name: 'item4' },
      { display_timeline: false, name: 'item5' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([
      { display_timeline: true, name: 'item1', duration: 2 },
      { display_timeline: true, name: 'item3', duration: 3 },
    ]);
  });

  it('should handle pattern with false items at the beginning', () => {
    const input = [
      { display_timeline: false, name: 'item1' },
      { display_timeline: true, name: 'item2' },
      { display_timeline: false, name: 'item3' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([
      { display_timeline: false, name: 'item1' },
      { display_timeline: true, name: 'item2', duration: 2 },
    ]);
  });

  it('should handle all items having display_timeline false', () => {
    const input = [
      { display_timeline: false, name: 'item1' },
      { display_timeline: false, name: 'item2' },
      { display_timeline: false, name: 'item3' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: false, name: 'item1', duration: 3 }]);
  });

  it('should preserve other properties when modifying duration', () => {
    const input = [
      { display_timeline: true, name: 'item1', id: 1, type: 'test' },
      { display_timeline: false, name: 'item2', id: 2, type: 'test2' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([
      { display_timeline: true, name: 'item1', id: 1, type: 'test', duration: 2 },
    ]);
  });

  it('should handle complex alternating pattern', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: false, name: 'item2' },
      { display_timeline: false, name: 'item3' },
      { display_timeline: true, name: 'item4' },
      { display_timeline: true, name: 'item5' },
      { display_timeline: false, name: 'item6' },
      { display_timeline: true, name: 'item7' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([
      { display_timeline: true, name: 'item1', duration: 3 },
      { display_timeline: true, name: 'item4' },
      { display_timeline: true, name: 'item5', duration: 2 },
      { display_timeline: true, name: 'item7' },
    ]);
  });

  it('should not mutate the original array', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: false, name: 'item2' },
    ];
    const originalInput = JSON.parse(JSON.stringify(input));
    removeConsecutiveDuplicates(input);
    expect(input).not.toEqual(originalInput);
  });

  it('should handle objects with undefined duration property', () => {
    const input = [
      { display_timeline: true, name: 'item1', duration: undefined },
      { display_timeline: false, name: 'item2', duration: undefined },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: true, name: 'item1', duration: 2 }]);
  });

  it('should handle objects with null duration property', () => {
    const input = [
      { display_timeline: true, name: 'item1', duration: null },
      { display_timeline: false, name: 'item2', duration: null },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: true, name: 'item1', duration: 2 }]);
  });

  it('should handle objects with no duration property', () => {
    const input = [
      { display_timeline: true, name: 'item1' },
      { display_timeline: false, name: 'item2' },
      { display_timeline: false, name: 'item3' },
    ];
    const result = removeConsecutiveDuplicates(input);
    expect(result).toEqual([{ display_timeline: true, name: 'item1', duration: 3 }]);
  });

  it('should handle large array efficiently', () => {
    const input = [];
    input.push({ display_timeline: true, name: 'item0' });
    for (let i = 1; i < 1000; i++) {
      input.push({ display_timeline: false, name: `item${i}` });
    }

    const result = removeConsecutiveDuplicates(input);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({ display_timeline: true, name: 'item0', duration: 1000 });
  });
});
