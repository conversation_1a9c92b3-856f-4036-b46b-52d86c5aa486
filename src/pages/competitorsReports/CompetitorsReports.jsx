import PageTitle from '@core/layout/PageTitle';
import PageContainer from '@core/layout/PageContainer';
import {
  PeriodSelector,
  FiltersContainer,
  ContentContainer,
  BrandSelector,
  Timeline,
  TrendsPopover,
} from './components';
import useBrands from './hooks/useBrands';
import useReportsList from './hooks/useReportsList';
import useWeeklyReport from './hooks/useWeeklyReport';
import { useState } from 'react';
import SectionTitle from './components/SectionTitle';
import SectionContainer from './components/SectionContainer';
import { Box, Typography } from '@mui/material';
import TrendGraph from './components/TrendGraph';
import Markdown from 'react-markdown';
import Loader from '@core/features/notifications/Loader';
import { dayjs } from '@core/../utils/dateUtils';

export default function CompetitorsReports() {
  const [selectedBrand, setSelectedBrand] = useState();
  const [selectedPeriod, setSelectedPeriod] = useState();

  const { brands, isLoading: brandsIsLoading } = useBrands();
  const { listOfReports, isLoading: reportsListIsLoading } = useReportsList(selectedBrand);
  const { report, isLoading: reportIsLoading } = useWeeklyReport(selectedBrand, selectedPeriod);

  const selectBrand = brand => {
    setSelectedBrand(brand);
  };

  const disabledBrandsFilter = !brands;
  const disabledPeriodsFilter = !listOfReports || brandsIsLoading;
  const showSummary = !!report?.report?.weekly_summary;
  const showTrends = !!report?.competitorsTrend;
  const showTimeline = !!report?.dailyTimeline;

  const selectedPeriodDate = listOfReports?.find(period => period.week === selectedPeriod)?.date
    ?.value;

  return (
    <PageContainer>
      <PageTitle title="Competitors Agent" />
      <ContentContainer>
        <FiltersContainer>
          <BrandSelector
            disabled={disabledBrandsFilter}
            brands={brands}
            selectedBrand={selectedBrand}
            selectBrand={selectBrand}
            loading={brandsIsLoading}
          />
          <PeriodSelector
            periods={listOfReports}
            disabled={disabledPeriodsFilter}
            selectedPeriod={selectedPeriod}
            setSelectedPeriod={setSelectedPeriod}
            loading={reportsListIsLoading}
          />
        </FiltersContainer>
        {reportIsLoading && <Loader />}
        {showSummary && (
          <SectionContainer>
            <SectionTitle
              title={`Summary: ${dayjs(selectedPeriodDate).format('MMM D')} - ${dayjs(selectedPeriodDate).add(6, 'day').format('MMM D')}`}
            />
            <Markdown
              components={{
                p: Typography,
              }}
            >
              {report?.report?.weekly_summary}
            </Markdown>
          </SectionContainer>
        )}
        {showTrends && (
          <SectionContainer>
            <Box
              style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '24px' }}
            >
              <Typography variant="h6">Trends</Typography>
              <TrendsPopover />
            </Box>
            <TrendGraph data={report?.competitorsTrend} />
          </SectionContainer>
        )}
        {showTimeline && (
          <SectionContainer>
            <SectionTitle title="Timeline" />
            <Timeline data={report?.dailyTimeline} brand={selectedBrand} />
          </SectionContainer>
        )}
      </ContentContainer>
    </PageContainer>
  );
}
