import { useTheme } from '@emotion/react';
import Node from './Node';
import { Box, Typography, useMediaQuery } from '@mui/material';
import ImageThumbnails from './ImageThumbnails';
import { TIMELINE_LEFT_OFFSET } from './constants';
import Line from './Line';

export default function Item({ title, tier, main, secondary, brand, date, isLast, duration }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  return (
    <Box
      style={{
        position: 'relative',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
      }}
    >
      <Node />
      {!isLast && <Line />}
      <Box
        style={{
          marginLeft: `calc(${TIMELINE_LEFT_OFFSET} + 50px)`,
          width: isMobile ? 'auto' : '500px',
          flex: isMobile ? 1 : 'none',
        }}
      >
        <Box>
          <Typography variant="h6">
            {title + (duration ? ` (duration: ${duration} days)` : '')}
          </Typography>
        </Box>
        <Typography variant="subtitle2" sx={{ m: '5px 0 5px 0' }}>
          Tier {tier}
        </Typography>
        <Box component="ul" style={{ listStyle: 'none', padding: 0, margin: 0 }}>
          {main && <Box component="li">{main}</Box>}
          {secondary && (
            <Box component="li" style={{ color: 'gray' }}>
              {secondary}
            </Box>
          )}
        </Box>
      </Box>
      {isMobile && (
        <Box style={{ marginTop: '15px', marginLeft: `calc(${TIMELINE_LEFT_OFFSET} + 50px)` }}>
          <ImageThumbnails brand={brand} date={date} isMobile={isMobile} />
        </Box>
      )}
      {!isMobile && <ImageThumbnails brand={brand} date={date} isMobile={isMobile} />}
    </Box>
  );
}
