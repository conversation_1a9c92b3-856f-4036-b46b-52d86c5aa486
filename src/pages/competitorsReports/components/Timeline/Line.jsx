import { useTheme } from '@emotion/react';
import { Box } from '@mui/material';
import { TIMELINE_LEFT_OFFSET, TIMELINE_ITEM_GAP } from './constants';

export default function Line() {
  const theme = useTheme();
  const primaryColor = theme.palette.primary.main;
  return (
    <Box
      style={{
        position: 'absolute',
        left: TIMELINE_LEFT_OFFSET,
        top: '1rem',
        width: '4px',
        transform: 'translateX(-50%)',
        backgroundColor: primaryColor,
        height: `calc(100% + ${TIMELINE_ITEM_GAP}`,
      }}
    />
  );
}
