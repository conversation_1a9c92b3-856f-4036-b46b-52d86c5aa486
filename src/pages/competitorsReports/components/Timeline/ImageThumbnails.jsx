import { Box } from '@mui/material';
import ImageDialog from './ImageDialog';
import { useState } from 'react';

const emptyImage =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InRyYW5zcGFyZW50Ii8+PC9zdmc+';

export default function ImageThumbnails({ brand, date, isMobile }) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const images = [
    `https://storage.googleapis.com/competitors-agent/screenshots/${date.replaceAll('-', '')}_${brand?.toLowerCase()}_1.jpg`,
    `https://storage.googleapis.com/competitors-agent/screenshots/${date.replaceAll('-', '')}_${brand?.toLowerCase()}_2.jpg`,
    `https://storage.googleapis.com/competitors-agent/screenshots/${date.replaceAll('-', '')}_${brand?.toLowerCase()}_3.jpg`,
  ];
  const [failedImages, setFailedImages] = useState(new Set());
  const [selectedImage, setSelectedImage] = useState('');

  const validImages = images.filter(img => !failedImages.has(img));

  const nextImage = validImages[validImages.indexOf(selectedImage) + 1];
  const prevImage = validImages[validImages.indexOf(selectedImage) - 1];

  const handleOpenDialog = () => {
    setSelectedImage(validImages[0]);
    setDialogOpen(true);
  };
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const onPrevious = prevImage
    ? () => {
        setSelectedImage(prevImage);
      }
    : null;
  const onNext = nextImage
    ? () => {
        setSelectedImage(nextImage);
      }
    : null;

  if (validImages.length === 0) {
    return null;
  }

  return (
    <>
      <Box
        onClick={handleOpenDialog}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 0,
          position: 'relative',
          width: isMobile ? '80px' : '110px',
          height: isMobile ? '80px' : '110px',
        }}
      >
        {[...validImages].reverse().map((img, i) => (
          <Box
            key={img}
            component="img"
            src={i === validImages.length - 1 ? img : emptyImage}
            alt="Thumbnail"
            onError={() => {
              setFailedImages(prev => new Set(prev).add(img));
            }}
            sx={{
              cursor: 'pointer',
              width: isMobile ? '80px' : '110px',
              height: isMobile ? '80px' : '110px',
              objectFit: 'cover',
              border: '1px solid #ccc',
              borderRadius: '5px',
              position: 'absolute',
              transform: `rotate(${(2 - i) * 3}deg)`,
            }}
          />
        ))}
      </Box>
      <ImageDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        imageSrc={selectedImage}
        onPrevious={onPrevious}
        onNext={onNext}
      />
    </>
  );
}
