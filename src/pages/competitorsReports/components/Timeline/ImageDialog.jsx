import { Dialog, Box, IconButton, CircularProgress } from '@mui/material';
import { ChevronLeft, ChevronRight, Close } from '@mui/icons-material';
import { useState, useEffect, useRef } from 'react';
import useSWR from 'swr';

const DEFAULT_ZOOM = 2;

// Image fetcher function for SWR - preloads images for caching
const imageFetcher = async url => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(url);
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = url;
  });
};

const ImageDialog = ({ open, onClose, imageSrc, onPrevious, onNext }) => {
  const [zoom, setZoom] = useState(DEFAULT_ZOOM);
  const imageRef = useRef(null);

  // Use SWR to cache images
  const {
    data: cachedImageSrc,
    isLoading: imageLoading,
    error,
  } = useSWR(imageSrc ? imageSrc : null, imageFetcher, {
    revalidateOnFocus: false,
    revalidateIfStale: false,
    revalidateOnReconnect: false,
  });

  const handleWheel = event => {
    event.preventDefault();
    event.stopPropagation();
    const delta = event.deltaY > 0 ? -0.4 : 0.4;
    setZoom(prevZoom => Math.max(0.6, Math.min(10, prevZoom + delta)));
  };

  // Triggered when the dialog is open
  const handleEntered = () => {
    imageRef?.current?.addEventListener('wheel', handleWheel, { passive: false });
  };

  // Triggered when the image changes
  useEffect(() => {
    if (imageRef?.current)
      imageRef?.current?.addEventListener('wheel', handleWheel, { passive: false });
  }, [imageRef?.current]);

  // handles keyboard navigation
  useEffect(() => {
    if (!open) return;

    const handleKeyDown = event => {
      if (event.key === 'ArrowLeft' && onPrevious) {
        handlePrevious();
      } else if (event.key === 'ArrowRight' && onNext) {
        handleNext();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Cleanup event listener on unmount
    return () => {
      imageRef?.current?.removeEventListener('wheel', handleWheel);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [open, onPrevious, onNext]);

  const handleClose = () => {
    setZoom(DEFAULT_ZOOM);
    onClose();
  };

  const handlePrevious = () => {
    if (onPrevious) {
      onPrevious();
    }
  };

  const handleNext = () => {
    if (onNext) {
      onNext();
    }
  };

  const handleImageClick = () => {
    setZoom(DEFAULT_ZOOM);
  };

  return (
    <Dialog
      TransitionProps={{ onEntered: handleEntered }}
      open={open}
      onClose={handleClose}
      maxWidth={false}
      fullScreen
      PaperProps={{
        sx: {
          width: '95vw',
          height: '90vh',
          margin: 'auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        },
      }}
    >
      <IconButton
        size="large"
        onClick={handleClose}
        sx={{
          position: 'fixed',
          top: '7vh',
          right: '4vw',
          zIndex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          color: 'white',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
          },
        }}
      >
        <Close />
      </IconButton>

      {onPrevious && (
        <IconButton
          size="large"
          onClick={handlePrevious}
          sx={{
            position: 'fixed',
            left: '5vw',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
            },
          }}
        >
          <ChevronLeft />
        </IconButton>
      )}

      {cachedImageSrc && (
        <Box
          ref={imageRef}
          component="img"
          src={cachedImageSrc}
          onClick={handleImageClick}
          sx={{
            maxWidth: '100%',
            maxHeight: '100%',
            objectFit: 'contain',
            transform: `scale(${zoom})`,
            transition: 'transform 0.1s ease-out',
            transformOrigin: 'top center',
            cursor: 'zoom-in',
          }}
        />
      )}
      {(imageLoading || error) && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
            position: 'absolute',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          {imageLoading && <CircularProgress />}
          {error && (
            <Box sx={{ color: 'text.secondary', textAlign: 'center' }}>Failed to load image</Box>
          )}
        </Box>
      )}

      {onNext && (
        <IconButton
          size="large"
          onClick={handleNext}
          sx={{
            position: 'fixed',
            right: '5vw',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
            },
          }}
        >
          <ChevronRight />
        </IconButton>
      )}
    </Dialog>
  );
};

export default ImageDialog;
