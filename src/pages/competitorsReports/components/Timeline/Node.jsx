import { useTheme } from '@emotion/react';
import { Box } from '@mui/material';
import { TIMELINE_LEFT_OFFSET } from './constants';

export default function Node() {
  const theme = useTheme();
  const primaryColor = theme.palette.primary.main;
  return (
    <Box
      style={{
        position: 'absolute',
        left: TIMELINE_LEFT_OFFSET,
        transform: 'translateX(-50%)',
        height: '2rem',
        width: '2rem',
        borderRadius: '50%',
        backgroundColor: 'white',
        border: '4px solid ' + primaryColor,
        zIndex: 10,
      }}
    />
  );
}
