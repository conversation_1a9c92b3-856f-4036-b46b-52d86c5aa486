import { Box } from '@mui/material';

export default function FiltersContainer({ children }) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        gap: 2,
        top: 0,
        backgroundColor: 'background.default',
        zIndex: 999, // Increased z-index
        padding: '7px 24px 24px 24px',
        borderBottom: '1px solid',
        borderColor: 'divider',
        width: '100%',
        left: 0, // Add this
        right: 0, // Add this
        margin: 0, // Add this
        boxSizing: 'border-box', // Add this
      }}
    >
      {children}
    </Box>
  );
}
