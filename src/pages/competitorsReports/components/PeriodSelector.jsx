import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import { useEffect } from 'react';
import { dayjs } from '@core/../utils/dateUtils';

export default function PeriodSelector({
  periods,
  disabled,
  selectedPeriod,
  setSelectedPeriod,
  loading,
}) {
  useEffect(() => {
    if (periods && periods.length > 0 && !selectedPeriod) {
      setSelectedPeriod(periods[0].week);
    }
  }, [periods, selectedPeriod, setSelectedPeriod]);

  const selectPeriod = event => {
    setSelectedPeriod(event.target.value);
  };

  const label = loading ? 'Loading...' : 'Select Period';

  return (
    <FormControl sx={{ flex: 1 }}>
      <InputLabel id="period-select-label">{label}</InputLabel>
      <Select
        sx={{
          backgroundColor: theme =>
            theme.components?.MuiTextField?.styleOverrides?.root?.backgroundColor,
        }}
        labelId="period-select-label"
        id="period-select"
        label={label}
        onChange={selectPeriod}
        disabled={disabled}
        value={selectedPeriod || ''}
      >
        {periods?.map(period => (
          <MenuItem key={period.week} value={period.week}>
            {`${period.week.substring(5)} - (${dayjs(period.date.value).format('MMM D')} - ${dayjs(period.date.value).add(6, 'day').format('MMM D')}) ${dayjs(period.date.value).format('YYYY')}`}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
