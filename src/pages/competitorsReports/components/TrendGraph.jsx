import { Box, useMediaQuery, useTheme } from '@mui/material';
import Plot from 'react-plotly.js';

const lineShift = i => (i % 2 === 1 ? Math.floor(i / 2) : -i / 2);

const TrendGraph = ({ data }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const brands = Object.keys(data || {});

  // Get unique dates from all brands
  const allDates = new Set();
  brands.forEach(brand => {
    data[brand].forEach(d => {
      allDates.add(d.date.value);
    });
  });
  const uniqueDates = Array.from(allDates).sort();

  const traces = brands.map((brand, index) => ({
    name: brand,
    x: data[brand].map(d => d.date.value),
    y: data[brand].map(d => d.tier + lineShift(index) * 0.04),
    type: 'scatter',
    mode: 'lines+markers',
    marker: { color: `hsl(${(index * 360) / brands.length}, 70%, 50%)` },
    line: { color: `hsl(${(index * 360) / brands.length}, 70%, 50%)` },
    hoverinfo: 'none',
  }));

  return (
    <>
      <Plot
        data={traces}
        layout={{
          title: {
            text: '90-day Market Movement.',
            font: { size: 22 },
          },
          xaxis: {
            title: '',
            tickformat: '%b %-d',
            tickangle: -45,
            tickmode: 'array',
            tickvals: uniqueDates,
          },
          yaxis: {
            title: { text: 'Tier', standoff: 10 },
            range: [0, 5.5],
            dtick: 1,
          },
          legend: {
            orientation: isMobile ? 'h' : 'v',
            x: isMobile ? 0 : 1,
            y: isMobile ? -0.3 : 1,
            xanchor: isMobile ? 'left' : 'left',
            yanchor: isMobile ? 'top' : 'top',
          },

          margin: { t: 60, b: isMobile ? 150 : 100, l: 40, r: isMobile ? 20 : undefined },
          height: 500,
          dragmode: false,
        }}
        config={{
          responsive: true,
          fillFrame: false,
          scrollZoom: false,
          displaylogo: false,
          displayModeBar: false,
        }}
        style={{ width: '100%', height: '500px' }}
      />
      <Box
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '20px',
          justifyContent: 'left',
          fontSize: '14px',
          color: '#666',
          paddingBottom: '20px',
        }}
      >
        <Box>
          <strong>Tier 1</strong>: No promotions
        </Box>
        <Box>
          <strong>Tier 2</strong>: Light promotions
        </Box>
        <Box>
          <strong>Tier 3</strong>: Moderate promotions
        </Box>
        <Box>
          <strong>Tier 4</strong>: High promotions
        </Box>
        <Box>
          <strong>Tier 5</strong>: Extreme promotions
        </Box>
      </Box>
    </>
  );
};
export default TrendGraph;
