import { useState } from 'react';
import { Box, IconButton, Popover, Typography } from '@mui/material';
import { Info } from '@mui/icons-material';

const content = `<b>Tier 1:</b> ≤5% discount — no promotional language; everyday pricing.<br/>
<b>Tier 2:</b> 5–20% — light offers, minimal urgency (e.g., “Special Offer”).<br/>
<b>Tier 3:</b> 20–40% — clear promos with some urgency (“Up to 30% Off”).<br/>
<b>Tier 4:</b> 40–60% — strong urgency and larger discounts (“Flash Sale”).<br/>
<b>Tier 5:</b> ≥60% — extreme urgency or sitewide deals (“Final Hours!”). `;

export default function TrendsPopover() {
  const [anchorEl, setAnchorEl] = useState(null);

  return (
    <>
      <IconButton
        size="small"
        sx={{ color: 'text.secondary' }}
        onClick={event => setAnchorEl(event.currentTarget)}
      >
        <Info fontSize="small" />
      </IconButton>
      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="body2" dangerouslySetInnerHTML={{ __html: content }} />
        </Box>
      </Popover>
    </>
  );
}
