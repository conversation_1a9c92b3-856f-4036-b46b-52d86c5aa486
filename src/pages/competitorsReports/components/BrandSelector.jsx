import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import { useEffect } from 'react';

export default function BrandSelector({ loading, disabled, brands, selectedBrand, selectBrand }) {
  const label = loading ? 'Loading...' : 'Select Brand';
  const defaultBrand = brands && brands.length > 0 ? brands[0].brand : '';

  useEffect(() => {
    if (brands && brands.length > 0 && !selectedBrand) {
      selectBrand(brands[0].brand);
    }
  }, [brands, selectedBrand, selectBrand]);

  return (
    <FormControl sx={{ flex: 1 }}>
      <InputLabel id="brand-select-label">{label}</InputLabel>
      <Select
        sx={{
          backgroundColor: theme =>
            theme.components?.MuiTextField?.styleOverrides?.root?.backgroundColor,
        }}
        labelId="brand-select-label"
        id="brand-select"
        label={label}
        onChange={event => selectBrand(event.target.value)}
        value={selectedBrand || defaultBrand}
        disabled={disabled}
      >
        {brands?.map(brand => (
          <MenuItem key={brand.brand} value={brand.brand}>
            {brand.brand}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
