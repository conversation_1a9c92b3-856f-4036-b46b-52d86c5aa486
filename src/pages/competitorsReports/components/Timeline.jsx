import Item from './Timeline/Item';
import Container from './Timeline/Container';
import NodesContainer from './Timeline/NodesContainer';
import { dayjs } from '@core/../utils/dateUtils';
import _ from 'lodash';
import { removeConsecutiveDuplicates } from '../utils';

export default function Timeline({ data, brand }) {
  data[1].display_timeline = false;
  data[2].display_timeline = false;
  const transformedData = removeConsecutiveDuplicates(_.cloneDeep(data));

  return (
    <Container>
      <NodesContainer>
        {transformedData.map((item, index) => (
          <Item
            key={index}
            title={dayjs(item.date.value).format('dddd, MMMM D')}
            tier={item.tier}
            main={item.main_story}
            secondary={item.secondary_story}
            brand={brand}
            date={item.date.value}
            isLast={index === transformedData.length - 1}
            duration={item.duration}
          />
        ))}
      </NodesContainer>
    </Container>
  );
}
