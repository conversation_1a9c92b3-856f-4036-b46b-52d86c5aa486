import { Box, List, ListItem, ListItemButton, ListItemIcon, ListItemText } from '@mui/material';

import GroupTitle from './GroupTitle';

const iconUrl = integration => `/integrations/${integration.slug}.png`;

const iconSx = {
  height: '30px',
  width: '30px',
  objectFit: 'contain',
};

const Group = ({ group, selectedIntegrationId, setSelectedIntegrationId }) => {
  const clickItem = id => {
    setSelectedIntegrationId(id);
  };

  return (
    <Box>
      <GroupTitle>{group.name}:</GroupTitle>
      <List sx={{ '& .MuiListItem-padding': { p: 0 } }}>
        {group.integrations.map(integration => (
          <ListItem
            key={integration.id}
            onClick={() => integration.isActive && clickItem(integration.id)}
          >
            <ListItemButton
              selected={selectedIntegrationId === integration.id}
              disabled={!integration.isActive}
            >
              <ListItemIcon>
                <Box component="img" sx={iconSx} src={iconUrl(integration)} />
              </ListItemIcon>
              <ListItemText primary={integration.name} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );
};

export default Group;
