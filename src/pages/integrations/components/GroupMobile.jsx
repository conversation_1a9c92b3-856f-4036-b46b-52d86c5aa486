import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  ListItemIcon,
  ListItemText,
} from '@mui/material';

import GroupTitle from './GroupTitle';
import { ExpandMore } from '@mui/icons-material';

const iconUrl = integration => `/integrations/${integration.slug}.png`;

const iconSx = {
  height: '30px',
  width: '30px',
  objectFit: 'contain',
};

const Group = ({ group }) => {
  return (
    <Box>
      <GroupTitle>{group.name}:</GroupTitle>
      {group.integrations.map(integration => (
        <Accordion key={integration.id} disabled={!integration.isActive}>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <ListItemIcon>
              <Box component="img" sx={iconSx} src={iconUrl(integration)} />
            </ListItemIcon>
            <ListItemText primary={integration.name} />
          </AccordionSummary>
          <AccordionDetails>
            <Box>{JSON.stringify(integration.instance?.metadata)}</Box>
          </AccordionDetails>
          <AccordionActions>
            <Button>Edit</Button>
          </AccordionActions>
        </Accordion>
      ))}
    </Box>
  );
};

export default Group;
