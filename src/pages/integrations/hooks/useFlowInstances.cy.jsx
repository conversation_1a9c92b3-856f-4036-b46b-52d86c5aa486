import React from 'react';
import useFlowInstances from './useFlowInstances';
import flowInstancesFixture from '../../../../cypress/fixtures/flowInstances.json';

// Test component that uses the useFlowInstances hook
const TestComponent = () => {
  const { flowInstances, error, isLoading, mutate } = useFlowInstances();

  if (isLoading) return <div data-testid="loading">Loading...</div>;
  if (error) return <div data-testid="error">Error: {error.message}</div>;

  return (
    <div>
      <div data-testid="flowInstances">
        {flowInstances.flowInstances.map(instance => (
          <div key={instance.id} data-testid={`flowInstance-${instance.id}`}>
            {Object.entries(instance).map(([key, value]) => (
              <div key={key} data-testid={`${instance.id}-${key}`}>
                {typeof value === 'object' ? JSON.stringify(value) : value}
              </div>
            ))}
          </div>
        ))}
      </div>
      <button data-testid="mutate-button" onClick={() => mutate()}>
        Mutate
      </button>
    </div>
  );
};

const compareFlowInstance = (instance, enrichedInstance) => {
  Object.entries(enrichedInstance).forEach(([key, value]) => {
    cy.get(`[data-testid="${instance.id}-${key}"]`, { log: false }).should('exist');
    if (typeof value === 'object') {
      cy.get(`[data-testid="${instance.id}-${key}"]`, { log: false }).should(
        'contain',
        JSON.stringify(value),
      );
    } else {
      cy.get(`[data-testid="${instance.id}-${key}"]`, { log: false }).should('contain', value);
    }
  });
};

describe('useFlowInstances Hook', () => {
  beforeEach(() => {
    cy.intercept('GET', '**/flow_instances', { fixture: 'flowInstances.json' }).as(
      'getFlowInstances',
    );
  });

  it('should fetch and display all flow instances data', () => {
    cy.mountWithSWR(<TestComponent />);

    cy.get('[data-testid="loading"]').should('be.visible');
    cy.wait('@getFlowInstances');
    cy.get('[data-testid="loading"]').should('not.exist');
    cy.get('[data-testid="flowInstances"]').should('be.visible');

    flowInstancesFixture.flowInstances.forEach(instance => {
      compareFlowInstance(instance, instance);
    });
  });

  it('should handle errors', () => {
    cy.intercept('GET', '**/flow_instances', { statusCode: 500, body: 'Server Error' }).as(
      'getFlowInstancesError',
    );

    cy.mountWithSWR(<TestComponent />);

    cy.wait('@getFlowInstancesError');
    cy.get('[data-testid="error"]').should('be.visible');
  });

  it('should use the correct API URL', () => {
    const expectedUrl = `${Cypress.env('VITE_INSIGHTS_APP_BFF_API_URL')}/flow_instances`;

    cy.intercept('GET', expectedUrl).as('getFlowInstances');

    cy.mountWithSWR(<TestComponent />);

    cy.wait('@getFlowInstances').then(interception => {
      expect(interception.request.url).to.equal(expectedUrl);
    });
  });

  it('should trigger mutate function when button is clicked', () => {
    const updatedFlowInstances = {
      flowInstances: flowInstancesFixture.flowInstances.map(instance => ({
        ...instance,
        name: `Updated ${instance.name}`,
      })),
    };

    cy.mountWithSWR(<TestComponent />);
    cy.wait('@getFlowInstances');

    cy.intercept('GET', '**/flow_instances', req => {
      req.reply({ body: updatedFlowInstances });
    }).as('getUpdatedFlowInstances');

    cy.get('[data-testid="mutate-button"]').click();

    cy.wait('@getUpdatedFlowInstances');

    updatedFlowInstances.flowInstances.forEach(instance => {
      compareFlowInstance(instance, instance);
    });
  });
});
