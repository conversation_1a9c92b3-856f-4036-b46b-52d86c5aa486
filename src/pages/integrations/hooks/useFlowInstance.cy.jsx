import React from 'react';
import useFlowInstance from './useFlowInstance';
import flowInstanceFixture from '../../../../cypress/fixtures/flowInstance.json';

// Test component that uses the useFlowInstance hook
const TestComponent = ({ flowId }) => {
  const { flowInstance, error, isLoading, mutate } = useFlowInstance(flowId);

  if (isLoading) return <div data-testid="loading">Loading...</div>;
  if (error) return <div data-testid="error">Error: {error.message}</div>;

  return (
    <div>
      <div data-testid="flowInstance">
        {Object.entries(flowInstance).map(([key, value]) => (
          <div key={key} data-testid={key}>
            {typeof value === 'object' ? JSON.stringify(value) : value}
          </div>
        ))}
      </div>
      <button data-testid="mutate-button" onClick={() => mutate()}>
        Mutate
      </button>
    </div>
  );
};

const compareFlowInstance = flowInstance => {
  Object.entries(flowInstance).forEach(([key, value]) => {
    cy.get(`[data-testid="${key}"]`, { log: false }).should('exist');
    if (typeof value === 'object') {
      cy.get(`[data-testid="${key}"]`, { log: false }).should('contain', JSON.stringify(value));
    } else {
      cy.get(`[data-testid="${key}"]`, { log: false }).should('contain', value);
    }
  });
};

describe('useFlowInstance Hook', () => {
  beforeEach(() => {
    cy.intercept('GET', '**/flow_instances/*', { fixture: 'flowInstance.json' }).as(
      'getFlowInstance',
    );
  });

  it('should fetch and display all flow instance data', () => {
    cy.mountWithSWR(<TestComponent flowId="bcbc73c1-a288-4548-9fe7-0f0d1332c918" />);

    cy.get('[data-testid="loading"]').should('be.visible');
    cy.wait('@getFlowInstance');
    cy.get('[data-testid="loading"]').should('not.exist');
    cy.get('[data-testid="flowInstance"]').should('be.visible');

    compareFlowInstance(flowInstanceFixture);
  });

  it('should handle errors', () => {
    cy.intercept('GET', '**/flow_instances/*', { statusCode: 500, body: 'Server Error' }).as(
      'getFlowInstanceError',
    );

    cy.mountWithSWR(<TestComponent flowId="bcbc73c1-a288-4548-9fe7-0f0d1332c918" />);

    cy.wait('@getFlowInstanceError');
    cy.get('[data-testid="error"]').should('be.visible');
  });

  it('should use the correct API URL', () => {
    const flowId = 'bcbc73c1-a288-4548-9fe7-0f0d1332c918';
    const expectedUrl = `${Cypress.env('VITE_INSIGHTS_APP_BFF_API_URL')}/flow_instances/${flowId}`;

    cy.intercept('GET', expectedUrl).as('getFlowInstance');

    cy.mountWithSWR(<TestComponent flowId={flowId} />);

    cy.wait('@getFlowInstance').then(interception => {
      expect(interception.request.url).to.equal(expectedUrl);
    });
  });

  it('should trigger mutate function when button is clicked', () => {
    const flowId = 'bcbc73c1-a288-4548-9fe7-0f0d1332c918';
    const updatedFlowInstance = { ...flowInstanceFixture, name: 'Updated Flow Instance' };

    cy.mountWithSWR(<TestComponent flowId={flowId} />);

    cy.intercept('GET', '**/flow_instances/*', req => {
      req.reply({ body: updatedFlowInstance });
    }).as('getUpdatedFlowInstance');

    cy.get('[data-testid="mutate-button"]').click();

    cy.wait('@getUpdatedFlowInstance');

    compareFlowInstance(updatedFlowInstance);
  });
});
