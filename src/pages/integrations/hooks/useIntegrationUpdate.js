import useMutate from '../../../core/hooks/useMutate';

const useIntegrationUpdate = (flowInstanceId, flowInstanceIntegrationId) => {
  const url = `${import.meta.env.VITE_INSIGHTS_APP_BFF_API_URL}/flow_instances/${flowInstanceId}/integrations/${flowInstanceIntegrationId}`;

  const { trigger, inProgress, error } = useMutate(url, 'PATCH');

  return { trigger, inProgress, error };
};

export default useIntegrationUpdate;
