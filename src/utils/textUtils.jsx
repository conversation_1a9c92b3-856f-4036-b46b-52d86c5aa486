import translations from '../locales/en.json';
import DOMPurify from 'dompurify';
import parse from 'html-react-parser';
DOMPurify.addHook('afterSanitizeAttributes', function (node) {
  if ('target' in node) {
    node.setAttribute('target', '_blank');
    node.setAttribute('rel', 'noopener');
  }
});

export const renderTextWithLinks = text => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.split(urlRegex).map((part, index) =>
    urlRegex.test(part) ? (
      <a key={index} href={part} target="_blank" rel="noopener noreferrer">
        {part}
      </a>
    ) : (
      part
    ),
  );
};

export const t = key => {
  return translations[key] || key;
};

export const renderHtml = text => parse(DOMPurify.sanitize(text));
