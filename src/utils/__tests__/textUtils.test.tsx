import { t, renderTextWithLinks } from '../textUtils';

describe('t', () => {
  it('returns the translation for a key when key exists', () => {
    expect(t('access_token')).toBe('Access token');
  });

  it('returns the key when the key does not exist', () => {
    expect(t('non_existent_key')).toBe('non_existent_key');
  });

  it('returns the key when the key is empty', () => {
    expect(t('')).toBe('');
  });

  it('returns the key when the key is null', () => {
    expect(t(null)).toBe(null);
  });

  it('returns the key when the key is undefined', () => {
    expect(t(undefined)).toBe(undefined);
  });
});

describe('renderTextWithLinks', () => {
  it('returns the text with links when the text contains URLs', () => {
    const text = 'This is a link to https://www.example.com';
    const result = renderTextWithLinks(text);
    expect(result).toStrictEqual([
      'This is a link to ',
      <a key={1} href="https://www.example.com" target="_blank" rel="noopener noreferrer">
        https://www.example.com
      </a>,
      '',
    ]);
  });

  it('returns the text without links when the text does not contain URLs', () => {
    const text = 'This text does not contain any URLs';
    const result = renderTextWithLinks(text);
    expect(result).toStrictEqual(['This text does not contain any URLs']);
  });

  it('returns the text without links when the text is an empty string', () => {
    const text = '';
    const result = renderTextWithLinks(text);
    expect(result).toStrictEqual(['']);
  });
});
