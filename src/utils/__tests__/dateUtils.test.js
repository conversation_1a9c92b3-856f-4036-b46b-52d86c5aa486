import { formatDateRespectingTimezone } from '../dateUtils';

describe('formatDateRespectingTimezone', () => {
  it('formats UTC date correctly', () => {
    const date = '2024-04-02T21:50:00.000Z';
    const formattedDate = formatDateRespectingTimezone(date);
    expect(formattedDate).toBe('April 2, 2024');
  });

  it('formats non-UTC date correctly', () => {
    const date = '2024-04-03T06:50:00.000+09:00';
    const formattedDate = formatDateRespectingTimezone(date);
    expect(formattedDate).toBe('April 3, 2024');
  });

  it('keeps UTC date even if local time is UTC+3', () => {
    // Mocking the local timezone to Athens, Greece (UTC+3)
    vi.useFakeTimers('modern');
    vi.setSystemTime(new Date('2024-04-02T21:50:00.000+03:00').getTime());

    const date = '2024-04-02T21:50:00.000Z';
    const formattedDate = formatDateRespectingTimezone(date);
    expect(formattedDate).toBe('April 2, 2024');

    vi.useRealTimers();
  });

  it('keeps UTC-10 date even if local time is UTC', () => {
    // Mocking the local timezone to UTC
    vi.useFakeTimers('modern');
    vi.setSystemTime(new Date('2024-04-02T21:50:00.000Z').getTime());

    const date = '2024-04-02T21:50:00.000-10:00';
    const formattedDate = formatDateRespectingTimezone(date);
    expect(formattedDate).toBe('April 2, 2024');

    vi.useRealTimers();
  });

  it('handles invalid date input gracefully', () => {
    const date = 'not-a-date';
    const formattedDate = formatDateRespectingTimezone(date);
    expect(formattedDate).toBe('Invalid Date');
  });
});
