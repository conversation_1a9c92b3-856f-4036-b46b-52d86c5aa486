import { displayValue, displayPercentage } from '../display';

describe('displayValue', () => {
  it('should return a formatted number with commas', () => {
    expect(displayValue(0)).toBe('0');
    expect(displayValue(1000)).toBe('1,000');
    expect(displayValue(1000000)).toBe('1,000,000');
  });

  it('should not return a sign', () => {
    expect(displayValue(-1000)).toBe('1,000');
  });

  it('should always round to the nearest integer', () => {
    expect(displayValue(1.11)).toBe('1');
    expect(displayValue(1.93)).toBe('2');
    expect(displayValue(1052.52)).toBe('1,053');
  });

  it('should handle wrong input', () => {
    expect(displayValue('1.11')).toBe('1');
    expect(displayValue('abc')).toBe('NaN');
    expect(displayValue(null)).toBe('0');
    expect(displayValue(undefined)).toBe('NaN');
  });
});

describe('displayPercentage', () => {
  it('should return a formatted percentage with one decimal place for (absolute) values less than 1', () => {
    expect(displayPercentage(0)).toBe('0.0');
    expect(displayPercentage(0.12)).toBe('0.1');
    expect(displayPercentage(0.59)).toBe('0.6');
    expect(displayPercentage(-0.59)).toBe('-0.6');
    expect(displayPercentage('0.12')).toBe('0.1');
  });

  it('should return a formatted percentage with no decimal places for (absolute) values greater than or equal to 1', () => {
    expect(displayPercentage(112.6)).toBe('113');
    expect(displayPercentage(1012.1)).toBe('1,012');
    expect(displayPercentage(-1012.1)).toBe('-1,012');
  });

  it('should handle wrong input', () => {
    expect(displayPercentage('abc')).toBe('NaN');
    expect(displayPercentage(null)).toBe(NaN);
    expect(displayPercentage(undefined)).toBe('NaN');
  });
});
