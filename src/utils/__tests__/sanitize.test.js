import { escapeChars } from '../sanitize';

describe('escapeChars', () => {
  it('should escape special characters in HTML', () => {
    expect(escapeChars('&')).toBe('&amp;');
    expect(escapeChars('<')).toBe('&lt;');
    expect(escapeChars('>')).toBe('&gt;');
    expect(escapeChars('"')).toBe('&quot;');
    expect(escapeChars("'")).toBe('&#039;');
    expect(escapeChars('=')).toBe('&#61;');
    expect(escapeChars('+')).toBe('&#43;');
    expect(escapeChars('-')).toBe('&#45;');
    expect(escapeChars('@')).toBe('&#64;');
    expect(escapeChars('|')).toBe('&#124;');
  });

  it('should not escape other characters', () => {
    expect(escapeChars('Hello, world!')).toBe('Hello, world!');
    expect(escapeChars('12345')).toBe('12345');
    expect(escapeChars('')).toBe('');
  });

  it('should work for multiple character instances anywhere in the string', () => {
    expect(escapeChars('Hello, <world>!')).toBe('Hello, &lt;world&gt;!');
    expect(escapeChars('Hello, "world"!')).toBe('Hello, &quot;world&quot;!');
    expect(escapeChars("Hello, 'wor+ld'!")).toBe('Hello, &#039;wor&#43;ld&#039;!');
  });
});
