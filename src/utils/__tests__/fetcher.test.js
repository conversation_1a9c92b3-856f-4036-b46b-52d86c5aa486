import Fetcher from '../fetcher';

describe('Fetcher', () => {
  const mockAccessToken = 'mock-access-token';
  let fetchMock;
  let consoleSpy;

  beforeEach(() => {
    fetchMock = vi.fn();
    global.fetch = fetchMock;
    consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.resetAllMocks();
    consoleSpy.mockRestore();
  });

  it('should create a fetcher function with the correct authorization header', async () => {
    const fetcher = Fetcher(mockAccessToken);
    const url = 'https://api.example.com/data';
    const mockResponse = { data: 'test' };

    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: vi.fn().mockResolvedValueOnce(mockResponse),
    });

    const result = await fetcher(url);

    expect(fetchMock).toHaveBeenCalledWith(url, {
      headers: { Authorization: `Bearer ${mockAccessToken}` },
    });
    expect(result).toEqual(mockResponse);
  });

  it('should merge custom options with the authorization header', async () => {
    const fetcher = Fetcher(mockAccessToken);
    const url = 'https://api.example.com/data';
    const customOptions = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ key: 'value' }),
    };

    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: vi.fn().mockResolvedValueOnce({}),
    });

    await fetcher(url, { arg: customOptions });

    expect(fetchMock).toHaveBeenCalledWith(url, {
      ...customOptions,
      headers: {
        ...customOptions.headers,
        Authorization: `Bearer ${mockAccessToken}`,
      },
    });
  });

  it('should throw an error when the response is not ok', async () => {
    const fetcher = Fetcher(mockAccessToken);
    const url = 'https://api.example.com/data';

    fetchMock.mockResolvedValueOnce({
      json: vi.fn(),
    });

    await expect(fetcher(url)).rejects.toThrow('Network response was not ok');
  });
});
