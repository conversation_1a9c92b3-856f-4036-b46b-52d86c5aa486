import enricher from '../enricher';
import { formatDateRespectingTimezone } from '../dateUtils';

vi.mock('../dateUtils', () => ({
  formatDateRespectingTimezone: vi.fn(),
}));

describe('enricher', () => {
  const mockIncident = {
    id: '123',
    metadata: {
      revenueExpectedDeviationPct: 5,
      revenueDelta: 1000,
      baseline: 5000,
      anomalyDetectionMode: 'TSA',
      lv1ExecutiveSummary: ['Summary 1', 'Summary 2'],
      lv2Overview: ['Overview 1', 'Overview 2'],
      lv2KeyInsights: ['Insight 1', 'Insight 2'],
      lv2Actions: ['Action 1', 'Action 2'],
      lv2VisualSummary: 'Visual Summary',
      productViewsRevenueImpact: 1000,
      cartsRevenueImpact: 2000,
      checkoutsRevenueImpact: 3000,
      ordersRevenueImpact: 4000,
      aovRevenueImpact: 5000,
      hypotheses: [
        {
          statement: 'Hypothesis 1',
          description: 'Description 1',
          analysis: 'Analysis 1',
          result_structured: [],
          verdict: 'retained',
        },
      ],
      lv3IncidentDetails: 'Incident Details',
      lv3RootCauseAnalysisSummary: 'RCA Summary',
      lv3RootCauseAnalysisDetails: 'RCA Details',
      lv3AISuggestions: 'AI Suggestions',
      metrics: [{ name: 'Metric 1', value: 100 }],
      actions: [
        {
          immediate: [{ id: 1, action: 'Immediate Action' }],
          long_term: [{ id: 1, action: 'Long Term Action' }],
        },
      ],
    },
    date: '2023-05-01T00:00:00Z',
    dimensions: [{ name: 'product category', value: 'Electronics' }],
    status: 'Open',
    assignedTo: { name: 'John Doe' },
    keyMetric: { value: '10000', last_value: '9000', symbol: 'USD' },
    incidentGraph: { time_series: [{ x: 1, y: 100 }] },
  };

  beforeEach(() => {
    formatDateRespectingTimezone.mockReturnValue('2023-05-01');
  });

  it('should enrich an incident correctly', () => {
    const enrichedIncident = enricher(mockIncident);

    expect(enrichedIncident).toEqual({
      id: '123',
      incidentTimeStamp: '2023-05-01',
      category: 'Electronics',
      issueStatus: 'Open',
      assignedResponsibility: 'John Doe',
      timeSeries: [{ x: 1, y: 100 }],
      currency: 'USD',

      revenueExpectedDeviationPct: 500,
      revenueDelta: 1000,
      baseline: 5000,
      anomalyDetectionMode: 'TSA',

      lv1ExecutiveSummary: ['Summary 1', 'Summary 2'],

      lv2Overview: ['Overview 1', 'Overview 2'],
      lv2KeyInsights: ['Insight 1', 'Insight 2'],
      lv2Actions: ['Action 1', 'Action 2'],
      lv2VisualSummary: 'Visual Summary',

      revenuePrevious: 9000,
      productViewsRevenueImpact: 1000,
      cartsRevenueImpact: 2000,
      checkoutsRevenueImpact: 3000,
      ordersRevenueImpact: 4000,
      aovRevenueImpact: 5000,
      revenueActual: 10000,
      hypotheses: [
        {
          statement: 'Hypothesis 1',
          description: 'Description 1',
          analysis: 'Analysis 1',
          result_structured: [],
          verdict: 'retained',
        },
      ],

      lv3IncidentDetails: 'Incident Details',
      lv3RootCauseAnalysisSummary: 'RCA Summary',
      lv3RootCauseAnalysisDetails: 'RCA Details',
      lv3AISuggestions: 'AI Suggestions',

      funnelMetrics: [{ name: 'Metric 1', value: 100 }],

      actions: {
        immediate: [{ id: 1, action: 'Immediate Action' }],
        long_term: [{ id: 1, action: 'Long Term Action' }],
      },

      rawIncident: mockIncident,
    });
  });

  it('should handle missing or undefined values', () => {
    const incompleteIncident = {
      id: '456',
      metadata: {},
      date: '2023-05-02T00:00:00Z',
      dimensions: [],
      status: 'Closed',
      assignedTo: null,
      keyMetric: {},
      incidentGraph: {},
    };

    const PLACEHOLDER = '<placeholder>';

    const enrichedIncident = enricher(incompleteIncident);

    expect(enrichedIncident).toMatchObject({
      id: '456',
      incidentTimeStamp: '2023-05-01',
      category: '',
      issueStatus: 'Closed',
      assignedResponsibility: undefined,
      timeSeries: undefined,
      currency: undefined,

      revenueExpectedDeviationPct: undefined,
      revenueDelta: undefined,
      baseline: undefined,
      anomalyDetectionMode: 'TSA',

      lv1ExecutiveSummary: [PLACEHOLDER, PLACEHOLDER],

      lv2Overview: [PLACEHOLDER, PLACEHOLDER],
      lv2KeyInsights: [PLACEHOLDER, PLACEHOLDER],
      lv2Actions: [PLACEHOLDER],
      lv2VisualSummary: PLACEHOLDER,

      revenuePrevious: 28713,
      productViewsRevenueImpact: undefined,
      cartsRevenueImpact: undefined,
      checkoutsRevenueImpact: undefined,
      ordersRevenueImpact: undefined,
      aovRevenueImpact: undefined,
      revenueActual: 71603,
      hypotheses: [
        {
          statement: '<placeholder>',
          description: '<placeholder>',
          analysis: '<placeholder>',
          result_structured: [],
          verdict: 'retained',
        },
        {
          statement: '<placeholder>',
          description: '<placeholder>',
          analysis: '<placeholder>',
          result: '<placeholder>',
          result_structured: [],
          verdict: 'retained',
        },
        {
          statement: '<placeholder>',
          description: '<placeholder>',
          analysis: '<placeholder>',
          result: '<placeholder>',
          result_structured: {
            lack_of_data: '<placeholder>',
            potential_internal_causes: '<placeholder>',
          },
          verdict: 'rejected',
        },
      ],

      lv3IncidentDetails: PLACEHOLDER,
      lv3RootCauseAnalysisSummary: PLACEHOLDER,
      lv3RootCauseAnalysisDetails: PLACEHOLDER,
      lv3AISuggestions: PLACEHOLDER,

      funnelMetrics: [],

      actions: {
        immediate: [
          { id: 1, action: '<placeholder>' },
          { id: 2, action: '<placeholder>' },
        ],
        long_term: [{ id: 1, action: '<placeholder>' }],
      },

      rawIncident: incompleteIncident,
    });

    expect(enrichedIncident.hypotheses).toHaveLength(3);
    expect(enrichedIncident.actions).toHaveProperty('immediate');
    expect(enrichedIncident.actions).toHaveProperty('long_term');
  });
});
