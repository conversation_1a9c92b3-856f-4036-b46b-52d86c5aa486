import React from 'react';
import ReactDOM from 'react-dom/client';
import './main.css';
import App from './core/App';

const root = ReactDOM.createRoot(document.getElementById('root'));

/* Mui TextField has a bug that causes endless re-renders when
wrapped in React.StrictMode. I'll only keep StrictMode for
development. See https://github.com/mui/material-ui/issues/33081 */
root.render(
  <>
    {import.meta.env.DEV ? (
      <React.StrictMode>
        <App />
      </React.StrictMode>
    ) : (
      <App />
    )}
  </>,
);
