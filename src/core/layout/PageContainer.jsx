import { Card, styled } from '@mui/material';

const PageContainerComp = styled(Card)(({ theme }) => ({
  width: 'inherit',
  [theme.breakpoints.up('xs')]: {
    minHeight: 'calc(100vh - 60px)',
    borderWidth: 0,
  },
  [theme.breakpoints.up('md')]: {
    minHeight: 'calc(100vh - 68px)',
    borderWidth: 1,
  },
}));

export default function PageContainer({ children }) {
  return <PageContainerComp variant="outlined">{children}</PageContainerComp>;
}
