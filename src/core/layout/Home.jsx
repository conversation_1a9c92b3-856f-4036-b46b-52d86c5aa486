import React, { useEffect, useContext } from 'react';
import { Box, Drawer } from '@mui/material';
import { withAuthenticationRequired, useAuth0 } from '@auth0/auth0-react';

import { State } from '../providers/State';
import Sidebar from '../layout/Sidebar';
import Menubar from '../layout/Menubar';
import Routing from '../Routing';
import HttpErrorNotif from '../features/notifications/HttpErrorNotif';

const claimKey = key => import.meta.env.VITE_AUTH0_NAMESPACE_MAIN + '/' + key;

const mainFrameSx = {
  m: '33px',
  width: '100%',
  display: 'flex',
  flexWrap: 'wrap',
  alignContent: 'flex-start',
  '@media (max-width: 900px)': {
    m: '64px 0 0 0',
  },
  '@media (max-width: 600px)': {
    m: '60px 0 0 0',
  },
};

function MainFrame({ children }) {
  return <Box sx={mainFrameSx}>{children}</Box>;
}

const drawerSx = {
  '& .MuiDrawer-paper': { width: 256 },
  display: { xs: 'none', md: 'block' },
  width: 256,
};

function Home() {
  const { isAuthenticated, _logout, getIdTokenClaims } = useAuth0();
  const { state, updateState } = useContext(State);

  useEffect(() => {
    if (!isAuthenticated) return;

    getIdTokenClaims().then(
      ({
        ['org_id']: auth0OrgID,
        [claimKey('displayName')]: displayName,
        [claimKey('email')]: email,
        [claimKey('connection')]: connection,
        [claimKey('name')]: name,
      }) => {
        updateState({ auth0OrgID, displayName, email, connection, name });
      },
    );
  }, [isAuthenticated]);

  return (
    state.auth0OrgID && (
      <>
        <Menubar />
        <Drawer variant="permanent" sx={drawerSx}>
          <Sidebar />
        </Drawer>
        <MainFrame>
          <Routing />
        </MainFrame>
        <HttpErrorNotif />
      </>
    )
  );
}

export default withAuthenticationRequired(Home);
