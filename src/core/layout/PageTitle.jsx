import { Box, styled, Typography } from '@mui/material';

const TitleContainer = styled(Box)({
  marginBottom: '24px',
  width: '100%',
  gap: '20px',
  borderBottom: '1px solid lightgray',
  display: 'flex',
  height: 'fit-content',
});

const Title = styled(Typography)({
  flexGrow: 1,
  margin: '24px',
  color: 'rgba(0,0,0,0.87)',
  fontSize: 20,
  fontFamily: 'Roboto',
  fontWeight: 500,
  lineHeight: '30px',
  letterSpacing: 0.15,
  display: { xs: 'none', sm: 'block' },
});

function PageTitle({ title }) {
  return (
    <TitleContainer data-testid="title-bar">
      <Title variant="h5">{title}</Title>
    </TitleContainer>
  );
}

export default PageTitle;
