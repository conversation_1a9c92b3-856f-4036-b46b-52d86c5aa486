import { Routes, Route, Navigate } from 'react-router-dom';

import RevenueInsights from '../pages/revenueInsights/RevenueInsights';
import ProtectedRoute from '@core/features/auth/ProtectedRoute';
import RIDetails from '../pages/revenueInsights/RIDetails';
import Login from '@core/features/auth/Login';
import RevenueInsightsSearch from '../pages/revenueInsights/RevenueInsightsSearch';
import Integrations from '../pages/integrations/Integrations';
import RevenueForecasts from '../pages/revenueForecasts/RevenueForecasts';

import { useContext, useEffect, useState } from 'react';
import { State } from '@core/providers/State';
import { Box, CircularProgress } from '@mui/material';
import CompetitorsReports from '../pages/competitorsReports/CompetitorsReports';
import TaggingAgent from '../pages/taggingAgent/TaggingAgent';

const Routing = () => {
  const { state } = useContext(State);
  const [defaultTab, setDefaultTab] = useState();
  useEffect(() => {
    if (state.flowInstances) {
      setDefaultTab(state.flowInstances[0]);
    }
  }, [state]);

  const flowExists = slug => state?.flowInstances?.find(x => x.slug === slug);

  if (!defaultTab)
    return (
      <Box display="flex" justifyContent="center">
        <CircularProgress />
      </Box>
    );

  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="" element={<Navigate to={defaultTab?.path} />} />
      <Route path="/" element={<Navigate to={defaultTab?.path} />} />
      {flowExists('revenue-insights') && (
        <Route
          path="/revenue-insights"
          element={
            <ProtectedRoute roles="USER">
              <RevenueInsights />
            </ProtectedRoute>
          }
        />
      )}
      {flowExists('revenue-insights') && (
        <Route
          path="/revenue-insights/:insightId"
          element={
            <ProtectedRoute roles="USER">
              <RIDetails />
            </ProtectedRoute>
          }
        />
      )}
      {flowExists('revenue-insights') && (
        <Route
          path="/revenue-insights/search"
          element={
            <ProtectedRoute roles="USER">
              <RevenueInsightsSearch />
            </ProtectedRoute>
          }
        />
      )}
      <Route
        path="/integrations/:flowInstanceId"
        element={
          <ProtectedRoute roles="USER">
            <Integrations />
          </ProtectedRoute>
        }
      />
      {flowExists('revenue-forecasts') && (
        <Route
          path="/revenue-forecasts"
          element={
            <ProtectedRoute roles="USER">
              <RevenueForecasts />
            </ProtectedRoute>
          }
        />
      )}
      {flowExists('competitors-reports') && (
        <Route
          path="/competitors-reports"
          element={
            <ProtectedRoute roles="USER">
              <CompetitorsReports />
            </ProtectedRoute>
          }
        />
      )}
      {flowExists('tagging-agent') && (
        <Route
          path="/tagging-agent"
          element={
            <ProtectedRoute roles="USER">
              <TaggingAgent />
            </ProtectedRoute>
          }
        />
      )}
    </Routes>
  );
};

export default Routing;
