import React from 'react';
import useRoleCheck from './useRoleCheck';

const TestComponent = ({ requiredRole }) => {
  return <div data-testid="role-check">{useRoleCheck(requiredRole).toString()}</div>;
};

describe('useRoleCheck Hook', () => {
  const testCases = [
    { roles: ['Insights App Admin'], requiredRole: 'ADMIN', expected: true },
    { roles: ['Insights App User'], requiredRole: 'ADMIN', expected: false },
    { roles: undefined, requiredRole: 'ADMIN', expected: false },
    { roles: ['Insights App User', 'Insights App Admin'], requiredRole: 'ADMIN', expected: true },
    { roles: [], requiredRole: 'ADMIN', expected: false },
    { roles: ['Insights App User'], requiredRole: 'USER', expected: true },
    { roles: ['Insights App Admin'], requiredRole: 'USER', expected: false },
    { roles: ['Insights App User', 'Insights App Admin'], requiredRole: 'USER', expected: true },
    { roles: [], requiredRole: 'USER', expected: false },
    {
      roles: ['Insights App User', 'Insights App Admin'],
      requiredRole: 'UNKNOWN',
      expected: false,
    },
  ];

  testCases.forEach(({ roles, requiredRole, expected }) => {
    it(`should return ${expected} when required role is ${requiredRole} and user roles are ${roles}`, () => {
      cy.mountAuthorized(<TestComponent requiredRole={requiredRole} />, roles);
      cy.get('[data-testid="role-check"]').should('have.text', expected.toString());
    });
  });
});
