import './App.css';
import { BrowserRouter as Router } from 'react-router-dom';
import { ThemeProvider } from '@mui/material';

import theme from '../theme/theme';
import Auth0ProviderWithHistory from './providers/Auth0ProviderWithHistory';
import Home from './layout/Home';
import StateProvider from './providers/State';
import SWRProvider from './providers/SWRProvider';

function App() {
  return (
    <StateProvider>
      <ThemeProvider theme={theme}>
        <Router>
          <Auth0ProviderWithHistory
            audience={import.meta.env.VITE_INSIGHTS_APP_API_AUTH0_AUDIENCE}
            domain={import.meta.env.VITE_AUTH0_DOMAIN}
            clientId={import.meta.env.VITE_AUTH0_CLIENT_ID}
          >
            <SWRProvider>
              <div className="App">
                <Home />
              </div>
            </SWRProvider>
          </Auth0ProviderWithHistory>
        </Router>
      </ThemeProvider>
    </StateProvider>
  );
}

export default App;
