import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import viteCompression from 'vite-plugin-compression';
import { visualizer } from 'rollup-plugin-visualizer';
import path from 'path';

export default defineConfig(({ mode }) => {
  // Load environment variables based on the current mode (development, production, etc.)
  // See https://vite.dev/config/ and https://vite.dev/guide/env-and-mode#env-files
  const env = loadEnv(mode, process.cwd(), '');
  const isPreview = process.env.npm_lifecycle_event === 'preview';

  return {
    plugins: [
      react(),
      // Generate Brotli compressed files
      viteCompression({
        algorithm: 'brotliCompress',
        threshold: 1024, // compress files larger than 1kb
      }),
      // Fallback to gzip for browsers that don't support Brotli
      viteCompression({
        algorithm: 'gzip',
        threshold: 1024,
      }),
      visualizer({
        open: isPreview, // only open automatically during preview
        gzipSize: true,
        brotliSize: true,
      }),
    ],
    test: {
      environment: 'jsdom', // Simulates a browser environment
      globals: true, // Enables `describe`, `it`, etc.
    },
    optimizeDeps: {
      exclude: ['@auth0/auth0-react'],
    },
    build: {
      rollupOptions: {
        input: {
          main: 'index.html',
          vendors: 'src/vendors.js',
        },
      },
      commonjsOptions: {
        exclude: ['@auth0/auth0-react'],
      },
      outDir: 'build', // Custom output dir because webpack was using "build" and vite uses "dist"
    },
    server: {
      host: true,
      port: env.PORT || 5173,
    },
    preview: {
      port: env.PORT || 4173,
    },
    resolve: {
      alias: {
        '@core': path.resolve(__dirname, 'src/core'),
      },
    },
  };
});
