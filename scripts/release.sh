#!/bin/bash

set -euo pipefail

# Step 1: Check out to test branch
git checkout test
git pull --all

# Step 1.5 Check for uncommitted changes and abort if found
if git diff --quiet; then
  echo "No uncommitted changes found."
else
  echo "Error: Uncommitted changes found. Please commit or stash them before proceeding."
  exit 1
fi

# Step 2: Get the latest tag that matches the pattern 'prod-r*'
latest_tag=$(git describe --tags `git rev-list --tags='prod-r*' --max-count=1`)

# Display the latest tag
echo
echo "Latest tag: $latest_tag"

# Step 3: Parse the number from the tag, increment it by 1, and prepare the new release tag
release_number=${latest_tag##*prod-r}
new_release_number=$((release_number + 1))
new_release_tag="prod-r$new_release_number"

# Display the new release number
echo "New tag: $new_release_tag"

# Step 4: Get the latest commit hash
latest_commit_hash=$(git rev-parse HEAD)

# Step 5: Display the latest commit hash & release logs
echo "Latest commit hash: $latest_commit_hash"
echo
echo "Release notes:"
git log --format="%h %s [%an]" origin/main..origin/test
echo

# Step 6: Create a new tag for the latest commit with the new release number, and push it
# Prompt for approval before proceeding
echo "-----"
echo
echo "Does everything look correct? Would you like to proceed with the production release? Replying yes will run the following commands:"
echo "git tag -a \"$new_release_tag\" $latest_commit_hash -m \"Production Release $(date +%Y-%m-%d) r$new_release_number\""
echo "git push origin \"$new_release_tag\""
read -p "(y/n)" -n 1 -r
echo    # Move to a new line
if [[ $REPLY =~ ^[Yy]$ ]]
then
    git tag -a "$new_release_tag" $latest_commit_hash -m "Production Release $(date +%Y-%m-%d) r$new_release_number"
    git push origin "$new_release_tag"
    echo "ok"
else
    echo "Operation cancelled."
fi
