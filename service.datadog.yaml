schema-version: v2.2
dd-service: insights-app-fe
team: insights-app
application: insights-app
description: "The frontend for the Insights App"
tier: "1"
lifecycle: "production"
type: web
languages:
  - js
contacts:
  - type: slack
    contact: https://baresquare.slack.com/archives/C066VFB9WSZ
links:
  - name: insights-app-fe
    type: repo
    provider: github
    url: https://github.com/BareSquare/insights-app-fe
  - name: Insights App Confluence Space
    type: doc
    provider: Confluence
    url: https://bare-square.atlassian.net/wiki/spaces/TIA/overview
  - name: Insights App JIRA project
    type: other
    url: https://bare-square.atlassian.net/jira/software/c/projects/INSIGHTS/boards/31
    provider: Jira
  - name: Insights App Miro board
    type: other
    url: https://miro.com/app/board/uXjVNLrEhKU=/
    provider: Miro