# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist

# misc
.DS_Store
*.env
.env.local
.env.development.local
.env.test.local
.env.production.local
cypress.env.json
.eslintcache
stats.html

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editors
.idea/

# Terraform
*.tfstate
*.tfstate.*
*.terraform.lock*
**/modules/**/*.zip
**/.terraform
**/.external_modules
**/.terraform-version
**/.local.auto.tfvars
