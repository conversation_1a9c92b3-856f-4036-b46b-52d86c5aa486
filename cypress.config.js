const { defineConfig } = require('cypress');
require('dotenv').config({ path: './.env.development' });

module.exports = defineConfig({
  env: {
    ...process.env,
  },
  chromeWebSecurity: false,
  e2e: {
    baseUrl: 'http://localhost:3001',
    experimentalRunAllSpecs: true,
    watchForFileChanges: false,
    viewportWidth: 1920,
    viewportHeight: 1080,
    setupNodeEvents(_on, _config) {
      // implement node event listeners here
    },
  },

  component: {
    watchForFileChanges: false,
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },
});
